import { Checkbox, InputNumber, Radio, Tooltip } from 'ant-design-vue';
import { cloneDeep } from 'lodash';
import { computed, defineComponent, nextTick, reactive } from 'vue';

import QIcon from '@/components/global/q-icon';
import QModal from '@/components/global/q-modal';
import QSwitch from '@/components/global/q-switch';
import { createPromiseDialog } from '@/components/promise-dialogs';
import { useSettingModalLogic } from '@/apps/setting/composables/use-setting-modal-logic';
import MultiSelect from '@/components/multi-select';

import RiskSettingsFilterCondition from '../../../../components/risk-settings-filter-condition';
import {
  fieldNameMap,
  operatorMap,
  optionLabelsMap,
  dimensionUnitMap,
  radioOptionsMap,
  conditionOptions,
  validatorMap,
} from './risk-settings.config';
import styles from './risk-settings.module.less';
import CompanySelect from '@/components/modal/supplier/company-select';

// 校验类型
const RiskFieldsMap = {
  duration: {
    validator: (v) => {
      if (v > 0) {
        return false;
      }
      return '请输入大于0的整数';
    },
  },
  registrationAmount: {
    validator: (v) => {
      if (v > 0) {
        return false;
      }
      return '请输入大于0的整数';
    },
  },
};

const leSettingList = ['duration', 'registrationAmount', 'paidInCapitalRatio'];

const geSettingList = [
  'amountInvolved',
  'assetLiabilityRatio',
  'landMortgageAmount',
  'AmountOwed',
  'limitCount',
  'equityAmount',
  'executionSum',
  'amount',
  'employeeReductionRatio',
  'registeredCapitalChangeRatio',
];

const SettingModal = defineComponent({
  name: 'SettingModal',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const modelType = computed(() => props.params.modelType || 'diligence');
    const { visible, data, updateItem, changeSetting, getError, handleSubmit } = useSettingModalLogic(
      RiskFieldsMap,
      {
        props,
        emit,
      },
      modelType.value
    );
    const updateModal = (key, val, flag = 1) => {
      if (modelType.value === 'diligence' && flag === 1) {
        data.value.strategyModel[key] = val;
      } else if (key === 'detailsParams') {
        data.value.params = val;
      } else {
        data.value[key] = val;
      }
    };

    const errorInfo = reactive<{ msg?: string; key?: string }>({
      msg: '',
      key: '',
    });

    const beforeSubmit = () => {
      const validatorList = validatorMap[data.value.key];
      if (validatorList?.length) {
        validatorList.forEach((item) => {
          if (item.validator && !item.validator(data.value)) {
            errorInfo.key = item.key;
            errorInfo.msg = item.message;
            throw new Error(item.message);
          }
        });
      }
      errorInfo.key = '';
    };

    const handleOk = async () => {
      try {
        beforeSubmit();
        handleSubmit();
      } catch (e) {
        console.log(e);
      }
    };

    return {
      visible,
      data,
      modelType,
      updateModal,
      updateItem,
      changeSetting,
      getError,
      handleSubmit,
      handleOk,
      errorInfo,
    };
  },
  render() {
    const basicData = this.modelType === 'diligence' ? this.data?.strategyModel : this.data;
    const filterData = this.modelType === 'diligence' ? basicData?.detailsParams : basicData?.params;

    const extra = this.modelType === 'diligence' ? '' : 'penaltiesType';
    const getTitleInfo = (field: string, dataKey: string) => {
      let title;
      let tooltip;

      if (field === 'dataRange') {
        const configs = {
          SuspectedInterestConflict: {
            title: '人员范围',
            tooltip: '可自由选择人员管理分组范围与准入企业进行疑似潜在利益冲突排查',
          },
          StaffWorkingOutsideForeignInvestment: {
            title: '人员范围',
            tooltip: '可自由选择人员管理分组范围与准入企业进行潜在利益冲突排查',
          },
          BlacklistPartnerInvestigation: {
            title: '黑名单范围',
            tooltip: '可自由选择内部黑名单分组及标签范围与准入企业进行关联关系排查',
          },
          BlacklistSuspectedRelation: {
            title: '黑名单范围',
            tooltip: '可自由选择内部黑名单分组及标签范围与准入企业进行疑似关联关系排查',
          },
          CustomerPartnerInvestigation: {
            title: '范围设置',
            tooltip: '可自由选择第三方列表企业范围与准入企业进行交叉重叠关系排查',
          },
          CustomerSuspectedRelation: {
            title: '范围设置',
            tooltip: '可自由选择第三方列表企业范围与准入企业进行交叉重叠疑似关联关系排查',
          },
          HitInnerBlackList: {
            title: '黑名单范围',
            tooltip: '可自由选择内部黑名单分组及标签范围与准入企业进行关联排查',
          },
        };
        const data = configs[dataKey] || {};
        title = data.title;
        tooltip = data.tooltip;
      } else if (
        field === 'types' &&
        [
          'CustomerPartnerInvestigation', // 与第三方列表企业存在投资任职关联
          'BlacklistPartnerInvestigation', // 与黑名单列表企业存在投资任职关联
        ].includes(this.data?.key)
      ) {
        title = '条件筛选';
      } else {
        title = fieldNameMap[field];
      }
      return {
        title,
        tooltip,
      };
    };

    const extraRenderFilters = filterData?.filter((item) => item.field !== 'isValid') || [];

    // isValid 字段没有用到，产品让隐藏，不走模型升级
    const hideIsValid = ['HitInnerBlackList'];
    const validFilter = hideIsValid.includes(this.data?.key) ? [] : filterData?.filter((item) => item.field === 'isValid') || [];

    const updateParams = (data) => {
      this.updateModal('detailsParams', [...data, ...validFilter]);
    };
    const renderBasic = (v, index?) => {
      let operatorOptions = cloneDeep(operatorMap);
      if ([...leSettingList, ...geSettingList].includes(v.field)) {
        // 历史租户的值没法变，这边进行兼容
        if (v.fieldOperator === 'le') {
          operatorOptions = [{ label: '小于等于', value: 'le' }];
        } else if (v.fieldOperator === 'lt') {
          operatorOptions = [{ label: '小于', value: 'lt' }];
        } else {
          operatorOptions = operatorOptions.filter((item) => item.value === v.fieldOperator);
        }
      }
      // 单位
      const unit = dimensionUnitMap[v.field] ?? '元';
      let content;
      switch (v.field) {
        case 'companyInfo':
          content = (
            <CompanySelect
              value={v.fieldVal.companyName}
              placeholder="请输入企业名称"
              class="w-full"
              onChange={(name, option) => {
                v.fieldVal.companyId = option.KeyNo;
                v.fieldVal.companyName = name;
              }}
            />
          );
          break;
        case 'isValid':
        case 'recentTime':
        case 'approachingExpiry':
          content = (
            <Radio.Group value={v.fieldVal} onChange={(e) => this.updateItem(v, e.target.value)}>
              {radioOptionsMap[v.field].map((item) => (
                <Radio value={item.value}>{item.label}</Radio>
              ))}
            </Radio.Group>
          );
          break;
        case 'judgementRoleExclude':
          content = (
            <div class={styles.riskModalItem}>
              <Checkbox.Group
                options={[
                  { label: '排除身份为原告案件', value: 'prosecutor' },
                  { label: '排除身份为第三人案件', value: 'thirdpartyrole' },
                ]}
                v-model={v.fieldVal}
              ></Checkbox.Group>
            </div>
          );
          break;
        case 'associateObject':
        case 'caseIdentity':
        case 'types':
        case 'dataRange':
        case 'blackListType':
        case 'businessStatus':
        case 'simpleCancellationStep': // 简易注销结果
        case 'reasonType': // 经营异常
        case 'foreignInvestmentChangeType': // 经营异常
        case extra:
          content = (
            <RiskSettingsFilterCondition
              dimension={this.data?.key}
              item={v}
              onChange={(changedParams) => {
                const detailParams = extraRenderFilters.slice();
                detailParams[index] = changedParams;
                updateParams(detailParams);
              }}
            />
          );
          break;
        case 'conditionOperator':
          content = <Radio.Group class={styles.conditionRadioGroup} v-model={v.fieldVal} options={conditionOptions}></Radio.Group>;
          break;
        default:
          content = (
            <div class={styles.riskModalItem} style={{ marginTop: '-5px', alignItems: 'center' }}>
              {(() => {
                // 大于小于
                if (
                  [
                    'penaltiesType',
                    'businessAbnormalType',
                    'punishReasonType',
                    'pledgeStatus',
                    'creditType',
                    'billAcceptanceRiskStatus',
                    'listtypecode',
                  ].includes(v.field)
                ) {
                  return null;
                }
                if (operatorOptions.length > 1) {
                  return (
                    <q-select
                      allowClear={false}
                      style={{ marginRight: '5px', width: '130px', height: '32px' }}
                      v-model={v.fieldOperator}
                      placeholder={v.fieldName || '请选择'}
                      options={operatorOptions}
                    />
                  );
                }
                return <div style={{ marginRight: '5px' }}>{operatorOptions[0].label}</div>;
              })()}
              {[
                'penaltiesType',
                'punishReasonType',
                'pledgeStatus',
                'creditType',
                'businessAbnormalType',
                'billAcceptanceRiskStatus',
                'listtypecode',
              ].includes(v.field) ? (
                <MultiSelect
                  style={{ width: '350px' }}
                  class={styles.input}
                  allowClear={true}
                  inline
                  value={v.fieldVal}
                  options={optionLabelsMap[`${this.data?.key}.${v.field}`] || optionLabelsMap[v.field]}
                  placeholder={v.fieldName || '请选择'}
                  onChange={(val) => {
                    v.fieldVal = val;
                  }}
                />
              ) : null}
              {![
                'penaltiesType',
                'businessAbnormalType',
                'punishReasonType',
                'pledgeStatus',
                'creditType',
                'billAcceptanceRiskStatus',
                'listtypecode',
              ].includes(v.field) ? (
                <div class={{ [styles.hasError]: Boolean(this.getError(v)) }}>
                  <InputNumber
                    style={{ marginRight: '5px' }}
                    class={styles.input}
                    v-model={v.fieldVal}
                    min={0}
                    precision={0}
                    formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
                  />
                  <div class={styles.errorText}>{this.getError(v) || ' '}</div>
                </div>
              ) : null}

              {unit}
            </div>
          );
          break;
      }
      return content;
    };

    const renderNormalFields = () => {
      if (!extraRenderFilters?.length) {
        return null;
      }

      return (
        <div class={styles.riskModalItem}>
          <div class={styles.title}>条件筛选</div>
          <div class={[styles.contentSpecial, styles.raw]}>
            {extraRenderFilters?.map((detailParam, index) => {
              const { title = detailParam.fieldName, tooltip } = getTitleInfo(detailParam.field, this.data?.key);
              return (
                <div class={styles.riskModalItemSpecial} key={`${index}-${detailParam.field}`}>
                  <div
                    class={{
                      [styles.title]: true,
                      [styles.required]: validatorMap[this.data?.key]?.find((v) => v.key === detailParam.field),
                    }}
                  >
                    {' '}
                    {tooltip ? (
                      <Tooltip placement="top" title={tooltip}>
                        <span>{title}</span>
                        <QIcon class={styles.icon} type="icon-a-shuomingxian" />
                      </Tooltip>
                    ) : (
                      title
                    )}
                  </div>
                  <div class={styles.content}>{renderBasic(detailParam, index)}</div>
                  <div v-show={this.errorInfo.key === detailParam.field} class="text-#f04040 ml-2">
                    {this.errorInfo.msg}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
    };

    return (
      <QModal
        visible={this.visible}
        title={this.data?.name}
        onOk={this.handleOk}
        size="large"
        onCancel={async () => {
          this.visible = false;
          await nextTick();
          this.$emit('resolve', false);
        }}
        viewportDistance={50 + 53 + 63} // top 50 + header 53 + bottom 63
      >
        <div class={styles.riskModal}>
          {/* 外部黑名单不支持调整指标类型 */}
          {/* 因为外部黑名单里面那么多指标项，命中其实都算一个维度。是不支持每个设置一般项和关键项的 */}
          {!['risk_outer_blacklist', 'HitOuterBlackList'].includes(this.params.riskKey) && this.modelType === 'diligence' && (
            <div class={styles.riskModalItem}>
              <div class={styles.title}>
                指标类型
                <q-glossary-info>
                  <div slot="tooltip">
                    关键项：用以标记为风险必查项，设置为关键项后，无论是否有风险命中，都会在排查详情中进行排查结果展示。
                    <br />
                    一般项：设置为一般项后，仅在命中风险事项才会在排查详情里展示，未命中风险时则不会在结果中展示。
                  </div>
                </q-glossary-info>
              </div>
              <div class={styles.content}>
                <Radio.Group value={this.data?.type} onChange={(e) => this.updateModal('type', e.target.value, 0)}>
                  <Radio value={'keyItems'}>关键项</Radio>
                  <Radio value={'generalItems'}>一般项</Radio>
                </Radio.Group>
              </div>
            </div>
          )}
          <div class={styles.riskModalItem}>
            <div class={styles.title}>风险等级</div>
            <div class={styles.content}>
              <Radio.Group class={styles.riskLevel} value={basicData?.level} onChange={(e) => this.updateModal('level', e.target.value)}>
                {this.modelType !== 'diligence' && (
                  <Radio value={0} class="level0">
                    提示风险
                  </Radio>
                )}
                <Radio value={1} class="level1">
                  关注风险
                </Radio>
                <Radio value={2} class="level2">
                  警示风险
                </Radio>
              </Radio.Group>
            </div>
          </div>

          {/* 周期 */}
          {basicData?.cycle ? (
            <div class={styles.riskModalItem}>
              <div class={styles.title}>统计周期</div>
              <div class={styles.content}>
                <Radio.Group value={basicData?.cycle} onChange={(e) => this.updateModal('cycle', e.target.value)}>
                  <Radio value={1}>近1年</Radio>
                  <Radio value={2}>近2年</Radio>
                  <Radio value={3}>近3年</Radio>
                  <Radio value={-1}>不限</Radio>
                </Radio.Group>
              </div>
            </div>
          ) : null}

          {/* 企业无有效关键资质或资质已过有效期 */}
          {/* 资质筛查 */}
          {[
            'NoCertification',
            'Certification',
            'MonitorCertificationExpired',
            this.modelType !== 'diligence' ? 'NoticeInTimePeriod' : '',
            'MonitorBlacklist',
          ].includes(this.data?.key) && extraRenderFilters.length > 0 ? (
            <div class={styles.riskModalItem}>
              <div class={styles.title}>条件筛选</div>
              <div class={[styles.content, styles.raw]}>
                {extraRenderFilters?.map((v, index) => {
                  return (
                    <div class={styles.extraCondition} key={v.key}>
                      <RiskSettingsFilterCondition
                        dimension={this.data?.key}
                        item={v}
                        onChange={(changedParams) => {
                          const detailParams = extraRenderFilters.slice();
                          detailParams[index] = changedParams;
                          updateParams(detailParams);
                        }}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          ) : null}

          {/* 税务处罚 */}
          {['TaxPenalties'].includes(this.data?.key) && extraRenderFilters.length > 0 ? (
            <div class={styles.riskModalItem}>
              <div class={styles.title}>条件筛选</div>
              <div class={[styles.content, styles.raw]}>
                {extraRenderFilters?.map((v, index) => {
                  return [
                    index === 0 ? null : <div class={styles.dividerOr}>或者</div>,
                    <div class={['flex', styles.innerFilter]}>
                      <div class={styles.titleInner}>{fieldNameMap[v.field] || v.fieldName}</div>
                      <div class={styles.content}>{renderBasic(v, index)}</div>
                    </div>,
                  ];
                })}
              </div>
            </div>
          ) : null}

          {/* 行政处罚 */}
          {['AdministrativePenalties'].includes(this.data?.key) && extraRenderFilters.length > 0 ? (
            <div class={styles.riskModalItem}>
              <div class={styles.title}>
                条件筛选
                <q-glossary-info
                  v-show={this.data?.key === 'AdministrativePenalties'}
                  tooltip="以“满足任意”连接多个筛选条件。即包含“处罚事由“或“处罚类型”或”处罚金额”的结果均会命中"
                />
              </div>
              <div class={[styles.content, styles.raw]}>
                {extraRenderFilters?.map((v, index) => {
                  return (
                    <div class={['flex', styles.innerFilter]}>
                      <div class={styles.titleInner}>{fieldNameMap[v.field] || v.fieldName}</div>
                      <div class={styles.content}>{renderBasic(v, index)}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : null}

          {/* 近3年负面新闻 */}
          {/* 3年外年负面新闻 */}
          {['NegativeNewsRecent', 'NegativeNewsHistory'].includes(this.data?.key) && extraRenderFilters.length > 0 ? (
            <div class={styles.riskModalItem}>
              <div class={styles.title}>条件筛选</div>
              <div class={[styles.content, styles.raw]}>
                {extraRenderFilters?.map((v, index) => {
                  return (
                    <div class={styles.extraCondition} key={v.key}>
                      <RiskSettingsFilterCondition
                        dimension={this.data?.key}
                        item={v}
                        onChange={(changedParams) => {
                          const detailParams = extraRenderFilters.slice();
                          detailParams[index] = changedParams;
                          updateParams(detailParams);
                        }}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          ) : null}

          {/* NOTE: 单独处理 `与第三方列表企业存在投资任职关联` */}
          {[
            'CustomerPartnerInvestigation', // 与第三方列表企业存在投资任职关联
            'CustomerSuspectedRelation', // 与第三方列表企业存在交叉重叠疑似关联
            'BlacklistPartnerInvestigation', // 与内部黑名单企业存在投资任职关联
            'BlacklistSuspectedRelation', // 与黑名单列表企业存在交叉重叠疑似关联
            // 'StaffWorkingOutsideForeignInvestment', // 潜在利益冲突
            'SuspectedInterestConflict', // 疑似潜在利益冲突
            'HitInnerBlackList', // 被列入内部黑名单
          ].includes(this.data?.key) && extraRenderFilters.length > 0
            ? extraRenderFilters?.map((detailParam, index) => {
                const { title, tooltip } = getTitleInfo(detailParam.field, this.data.key);

                return (
                  <div class={styles.riskModalItem} key={`${index}-${detailParam.field}`}>
                    <div class={styles.title}>
                      {tooltip ? (
                        <Tooltip placement="top" title={tooltip}>
                          <span>{title}</span>
                          <QIcon class={styles.icon} type="icon-a-shuomingxian" />
                        </Tooltip>
                      ) : (
                        title
                      )}
                    </div>

                    <div class={[styles.content, styles.raw]}>
                      <div class={styles.extraCondition}>
                        <RiskSettingsFilterCondition
                          dimension={this.data?.key}
                          item={detailParam}
                          onChange={(changedParams) => {
                            const detailParams = extraRenderFilters.slice();
                            detailParams[index] = changedParams;
                            updateParams(detailParams);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })
            : null}

          {/* 非特殊处理的日期、金额类型等设置 , */}
          {![
            'NegativeNewsRecent',
            'NegativeNewsHistory',
            // 准入排查
            'AdministrativePenalties',
            'TaxPenalties',
            'Certification',
            'CustomerPartnerInvestigation', // 与第三方列表企业存在投资任职关联
            'CustomerSuspectedRelation', // 与第三方列表企业存在交叉重叠疑似关联
            'BlacklistPartnerInvestigation', // 与内部黑名单企业存在投资任职关联
            'BlacklistSuspectedRelation', // 与黑名单列表企业存在交叉重叠疑似关联
            // 'StaffWorkingOutsideForeignInvestment', // 潜在利益冲突
            'SuspectedInterestConflict', // 疑似潜在利益冲突
            // 合作监控
            'MonitorCertificationExpired', // 有效关键资质临期提醒
            this.modelType !== 'diligence' ? 'NoticeInTimePeriod' : '', //  短期多起开庭公告
            'MonitorBlacklist', // 黑名单
            'foreignInvestmentChangeType',
            'HitInnerBlackList', // 被列入内部黑名单
          ].includes(this.data?.key) && renderNormalFields()}

          {/* 日期、金额类型设置 */}
          {validFilter?.map((v, index) => {
            return (
              <div class={styles.riskModalItem} key={index}>
                <div class={styles.title}>{v.fieldName || fieldNameMap[v.field]}</div>
                <div class={styles.content}>{renderBasic(v)}</div>
              </div>
            );
          })}

          <div class={styles.riskModalItem}>
            <div class={styles.title}>启用指标</div>
            <div class={styles.content}>
              <QSwitch size="medium" checked={this.data?.status === 1} onChange={() => this.changeSetting(this.data)} />
            </div>
          </div>
        </div>
      </QModal>
    );
  },
});

export default SettingModal;

export const openSettingModal = createPromiseDialog(SettingModal);
