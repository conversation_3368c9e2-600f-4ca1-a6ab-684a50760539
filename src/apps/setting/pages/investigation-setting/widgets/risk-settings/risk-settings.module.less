@import '@/styles/token.less';
@import '@/apps/setting/pages/investigation-setting/widgets/risk-settings/risk-level.module.less';

.container {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .header {
    font-weight: @qcc-font-bold;
    font-size: 16px;

    &.sub {
      font-size: 14px;
      line-height: 22px;
      font-weight: @qcc-font-normal;
    }
  }

  .body {
    margin-top: 10px;

    :global {
      td {
        white-space: pre-wrap;
      }
    }
  }

  .child {
    overflow: hidden; /* 防止内容超出容器 */
    display: none; /* 初始隐藏 */
    height: auto; /* 初始高度为 0 */
  }

  .show {
    animation: fadein 0.51s;
    display: block;
  }

  .hide {
    animation: fadeout 0.51s;
  }

  /* 移出动画 */
  @keyframes fadeout {
    0% {
      opacity: 1;
      display: block;
    }

    100% {
      opacity: 0;
      display: block;
    }
  }

  /* 进入动画 */
  @keyframes fadein {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
}

.risk-filter {
  display: flex;

  :global {
    .anticon-menu {
      cursor: pointer;

      &:hover {
        color: #128bed;
      }
    }
  }

  .menu {
    width: 140px;
    margin-right: 10px;
    border-right: 1px solid #e4edff;

    :global {
      .ant-menu-vertical {
        border-right: 0;
      }

      .ant-menu-vertical .ant-menu-item {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
      }
    }
  }

  .main {
    flex: 1;
  }
}

.risk-title {
  display: inline-block;
  margin-right: 10px;
}

.checkbox-group {
  :global {
    .ant-checkbox-group-item {
      margin-right: 14px;
    }
  }
}

.risk-modal {
  .risk-modal-item {
    min-height: 22px;
    line-height: 22px;
    display: flex;

    & + .risk-modal-item {
      margin-top: 15px;
    }

    :global {
      .ant-radio-group {
        vertical-align: top;
      }

      .ant-checkbox-group-item {
        min-width: 75px;
        margin-right: 20px;
      }

      .ant-checkbox-wrapper {
        &:hover {
          color: #128bed;
        }

        &.ant-checkbox-wrapper-checked {
          color: #128bed;
        }
      }
    }
  }

  .title {
    color: #333;
    width: 95px;
    line-height: 22px;
    flex-shrink: 0;

    // tooltip icon
    .icon {
      color: #bbb;
      margin-left: 5px;
      cursor: pointer;
    }
  }

  .required {
    position: relative;

    &::before {
      content: '*';
      position: absolute;
      left: -6px;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 50%;
      color: #f04040;
    }
  }

  .content {
    padding-left: 10px;
    flex: 1;

    &.raw {
      padding-left: 0;
    }
  }

  .content-special {
    flex: 1;
    margin: 0 10px;

    .title {
      color: #999;
      margin: 10px 0 5px;
      width: auto;
    }

    .risk-modal-item-special {
      position: relative;

      &:first-child {
        .title {
          margin-top: 0;
        }
      }

      :global {
        .third-black-switch {
          position: absolute;
          left: 100px;
          top: 2px;
        }
      }
    }
  }

  .input {
    height: 32px;
    width: auto;
    min-width: 200px;
    border-radius: 2px;
  }

  .innerFilter {
    height: 32px;
    margin: 0 0 5px 10px;
    display: flex;
    align-items: center;

    .titleInner {
      color: #999;
      flex-shrink: 0;
    }
  }

  :global {
    .ant-radio-group {
      line-height: 22px;
      display: inline-flex;
    }

    .ant-radio-wrapper {
      margin-right: 20px;
      min-width: 75px;
      line-height: inherit;

      span.ant-radio + * {
        padding-left: 0;
        padding-right: 0;
        margin-left: 4px;
        vertical-align: middle;
      }
    }
  }
}

.hasError {
  .input {
    border: 1px solid #f04040;
    box-shadow: none;
  }

  .errorText {
    color: #f04040;
  }
}

.extraCondition:not(:last-child) {
  margin-bottom: 15px;
}

.conditionRadioGroup {
  :global {
    .ant-radio-wrapper {
      width: 100px;
      margin-right: 0;
    }
  }
}