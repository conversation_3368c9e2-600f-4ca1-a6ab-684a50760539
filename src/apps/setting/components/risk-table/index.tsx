import { defineComponent, PropType } from 'vue';
import { <PERSON><PERSON>, message } from 'ant-design-vue';

import QTag from '@/components/global/q-tag';
import { RISK_LEVEL_CODE_THEME_MAP } from '@/shared/constants/risk-level-code-map.constant';
import QSwitch from '@/components/global/q-switch';
import { createFunctionalEventEmitter } from '@/utils/component';
import { hasPermission } from '@/shared/composables/use-permission';
import QRichTable from '@/components/global/q-rich-table';
import QIcon from '@/components/global/q-icon';
import { useUserStore } from '@/shared/composables/use-user-store';

import styles from './risk-table.module.less';
import {
  settingConditionsFilter,
  validatorMap,
} from '@/apps/setting/pages/investigation-setting/widgets/risk-settings/risk-settings.config';
import ClampContent from '@/components/clamp-content';
import { conditionFilterCustomRender } from '@/apps/setting/pages/cooperative-monitor-setting/modules/risk-dynamics/widgets/risk-settings/risk-settings.config';

const RiskTable = defineComponent({
  functional: true,
  props: {
    columns: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    dataSource: {
      type: Array as PropType<any[]>,
      required: true,
    },
    riskStatus: {
      type: Number,
      required: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  render(h, { props, listeners }) {
    const emitters = createFunctionalEventEmitter(listeners);
    const handleEdit = emitters('edit');
    const handleSort = emitters('sort');
    const handleSwitch = emitters('switch');
    const { isMultiple } = useUserStore();

    return (
      <QRichTable
        class={styles.container}
        rowKey={'key'}
        showIndex={false}
        draggable
        loading={props.loading}
        onDrag={handleSort}
        dataSource={props.dataSource}
        columns={props.columns}
        pagination={false}
        bordered
        scopedSlots={{
          draggle: () => {
            return <QIcon class={[styles.icon, !props.disabled ? 'drag-handle' : 'drag-handle-disabled']} type="icon-icon_tuodong" />;
          },
          riskLevel: (item) => {
            const schema = RISK_LEVEL_CODE_THEME_MAP[item?.strategyModel?.level || item.level];
            if (schema) {
              return (
                <QTag
                  style={{ height: '20px', lineHeight: '18px', padding: '1px 4px', color: '#333' }}
                  customClass={styles[schema.type]}
                  type={schema.type}
                >
                  {schema.label}
                </QTag>
              );
            }
            return '-';
          },
          enabled: (item: any) => {
            const handleValidate = (rest?) => {
              const validList = validatorMap[item.key];
              if (validList?.length) {
                validList.forEach((v) => {
                  if (v.validator && !v.validator({ ...item, ...rest })) {
                    message.error(v.message);
                    throw new Error(v.message);
                  }
                });
              }
            };
            return (
              <QSwitch
                disabled={props.disabled || !hasPermission([2082])}
                size="medium"
                checked={item.status === 1}
                onChange={(val) => {
                  try {
                    handleValidate({ status: val ? 1 : 0 });
                    handleSwitch(item, true);
                  } catch (e) {
                    console.log(e);
                  }
                }}
              />
            );
          },
          action: (item) => {
            return (
              <Button
                v-permission={[2082]}
                type="link"
                onClick={() => handleEdit(item)}
                disabled={props.disabled}
                style={{ color: props.isEdit || isMultiple.value ? '' : '#b8dcfa' }}
              >
                编辑
              </Button>
            );
          },
          riskName: (record) => {
            return (
              <div>
                <div>{record.name}</div>
                <div class={styles.namedes}>{record.description}</div>
              </div>
            );
          },
          investigationCondition: (record) => {
            const content = settingConditionsFilter(record);
            return (
              <ClampContent line={8} clampKey={record.key}>
                {content}
              </ClampContent>
            );
          },
          monitorCondition: (record) => {
            const content = conditionFilterCustomRender(record);
            return (
              <ClampContent line={8} clampKey={record.key}>
                {content}
              </ClampContent>
            );
          },
        }}
      />
    );
  },
});

export default RiskTable;
