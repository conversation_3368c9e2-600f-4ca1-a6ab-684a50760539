import { defineComponent, type PropType, type VNode } from 'vue';
import { Tooltip } from 'ant-design-vue';
import { flatten, isObject, uniq } from 'lodash';

import QTag from '@/components/global/q-tag';
import { getTenderRelationLevelStyle } from '@/shared/config/bidding-investigation-detail.config';
import { createFunctionalEventEmitter } from '@/utils/component';
import { getRoles } from '@/shared/components/_helpers/data-parser/data-node';
import QEntityLink from '@/components/global/q-entity-link';

import { preDelDetailData } from './use-data-source-hook';
import styles from './relational-path.module.less';

export type PathNode = {
  type: 'node';
  id: string;
  name: string;
  label: string;
};
export type PathEdge = {
  type: 'edge';
  roles: string[];
  direction: 'left' | 'right' | 'bidirectional';
  startid?: string;
  endid?: string;
  [x: string]: any;
};

export type PathElement = PathNode | PathEdge;

const getData = (data, roles) => {
  if (!Array.isArray(data)) {
    return [];
  }

  const arr = data.flatMap((item2) => {
    if (item2.type === 'ContactNumber' && item2.data?.some(isObject)) {
      return {
        ...item2,
        data: getRoles(item2.data),
      };
    }
    return item2;
  });
  if (roles?.length) {
    return roles?.map((item) => {
      const find = arr.find((v) => v.role === item);
      return {
        ...find,
        role: find?.role || item,
        data: find?.data || [],
      };
    });
  }
  return arr;
};

const renderContent = (item, value, elements) => {
  if (!item.data?.length) {
    return item.role;
  }

  // FIXME: 将基于 item.type 的逻辑处理抽象到一处
  const totalDataMap = {
    ContactNumber: () => value.data.find((vItem) => vItem.type === 'ContactNumber').data,
    Mail: () => item.rawData ?? item,
  };
  const { isShowMore, handleShowMore } = preDelDetailData(totalDataMap[item.type]?.() || item.data, elements);

  let tooltipText;
  if (item?.data?.some(isObject)) {
    if (item.type === 'Address') {
      tooltipText = item.data
        .filter(({ address }) => !!address)
        .map(({ address }) => address)
        .join(', ');
    }
  } else {
    tooltipText = uniq(item.data)?.join(', ');
  }

  if (!tooltipText) {
    return item.role;
  }

  const hasShowMore =
    ['ContactNumber', 'Address', 'HisEmploy', 'HisLegal', 'HisInvest', 'Mail'].includes(item.type) && isShowMore(item.type);
  return (
    <Tooltip>
      <div slot="title">
        {tooltipText}{' '}
        <a v-show={hasShowMore} onClick={() => handleShowMore(item.type, item)} style={{ whiteSpace: 'nowrap' }}>
          查看更多
        </a>
      </div>
      <span style={{ cursor: hasShowMore ? 'pointer' : 'none' }}>{item.role}</span>
    </Tooltip>
  );
};

const Node = defineComponent({
  functional: true,
  props: {
    value: {
      type: Object as PropType<PathNode>,
      required: true,
    },
  },
  render(h, { props }) {
    return <QEntityLink class={styles.node} coyObj={{ Name: props.value.label, KeyNo: props.value.id }} />;
  },
});

const Edge = defineComponent({
  functional: true,
  props: {
    value: {
      type: Object as PropType<PathEdge>,
      required: true,
    },
    elements: {
      type: Array as PropType<PathElement[]>,
    },
  },
  render(h, { props, listeners }) {
    const {
      data = [],
      roles,
      direction,
      render = () => {
        const newData = getData(data, roles);
        if (newData.length) {
          return newData.flatMap((item, index) => [index !== 0 ? ', ' : '', renderContent(item, props.value, props.elements)]);
        }
        return null;
      },
    } = props.value;
    const label = render?.() || roles?.join(',');
    // 通过label来判断，roleType不全，针对合并过的边就失效了
    const flattenLabel = Array.isArray(label) ? flatten(label).filter((item) => item) : label;
    const isLink = flattenLabel?.includes('控制关系') || props.value.showDetail;
    const emitters = createFunctionalEventEmitter(listeners);

    return (
      <span
        class={{
          [styles.edge]: true,
          [styles[direction]]: true,
        }}
      >
        <span
          class={{ [styles.label]: true, [styles.link]: isLink }}
          onClick={() => {
            if (!isLink) return;
            emitters('click')(props.value);
          }}
        >
          {label}
        </span>
        <span class={styles.line} />
      </span>
    );
  },
});

const RelationalPath = defineComponent({
  functional: true,
  props: {
    namespace: {
      type: String,
      default: '',
    },
    elements: {
      type: Array as PropType<PathElement[]>,
      default: () => [],
    },
    // 关系类型
    type: {
      type: [String, Array],
      default: '',
    },
  },
  render(h, { props, listeners }) {
    const { elements, type } = props;
    const tagData = getTenderRelationLevelStyle(type);
    const emitters = createFunctionalEventEmitter(listeners);
    return (
      <div class={styles.container}>
        {tagData.label ? <QTag style={{ ...tagData.style, margin: '0 5px', transform: 'translateY(-1px)' }}>{tagData.label}</QTag> : null}
        {elements.map(({ type: nodeType, ...element }: PathElement, index: number) => {
          const key = `${props.namespace}-${type}-${index}`;
          let node: VNode | null = null;

          if (nodeType === 'node') {
            node = <Node key={key} value={element as any} />;
          } else if (nodeType === 'edge') {
            node = (
              <Edge
                key={key}
                value={element as any}
                elements={elements}
                onClick={(data) => {
                  emitters('clickEdge')(data);
                }}
              />
            );
          } else {
            node = null;
          }
          return node;
        })}
      </div>
    );
  },
});

export default RelationalPath;
