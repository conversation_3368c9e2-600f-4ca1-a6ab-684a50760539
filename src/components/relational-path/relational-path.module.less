@import '@/styles/token.less';

@edge-color: #d6d6d6;
@edge-arrow-width: 15px;

.container {
  line-height: 28px;
  color: inherit;

  .node {
    :global{
      a {
        color: #333;
        white-space: break-spaces;

        &:hover{
          color: #118BED;
        }
      }
    }
  }

  .node,
  .edge {
    &:not(:last-child) {
      margin-right: 2px;
    }
  }

  .edge {
    white-space: nowrap;
    position: relative;
    padding: 0 @edge-arrow-width;
    max-width: 100%;
    overflow: hidden;

    &.left {
      .line::after {
        display: none;
      }
    }

    &.right {
      .line::before {
        display: none;
      }
    }

    &.null {
      .line {
        &::before,
        &::after {
          display: none;
        }
      }
    }

    .label {
      max-width: calc(100% - 30px);
      font-size: 12px;
      line-height: 18px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: @qcc-color-blue-500;
      text-align: center;
      transform: translateY(-3px);
      display: inline-block;
    }

    .link {
      cursor: pointer;

      &:hover {
        color: #0069bf;
      }
    }

    .line {
      position: absolute;
      height: 1px;
      background-color: @edge-color;
      transform: translateY(2px);
      top: 50%;
      left: @edge-arrow-width / 2;
      right: @edge-arrow-width / 2;

      &::before,
      &::after {
        content: '';
        position: absolute;
        transform: translateY(-50%);
        top: calc(50%);
        height: 0;
        width: 0;
        border: 0 solid rgba(0, 0, 0, 0);
      }
      // Left
      &::before {
        border-right-color: @edge-color;
        border-width: 3px 10px;
        left: @edge-arrow-width * -1;
      }
      // Right
      &::after {
        border-left-color: @edge-color;
        border-width: 3px 10px;
        right: @edge-arrow-width * -1;
      }
    }
  }
}
