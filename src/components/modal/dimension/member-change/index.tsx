import QEntityLink from '@/components/global/q-entity-link';
import QGlossaryInfo from '@/components/global/q-glossary-info';
import { formatEmployeeData, getOperTypeLabelMapper, getPartnerChangeDescMap } from '@/utils/firm';
import { dateFormat } from '@/utils/format';
import { computed, defineComponent, onMounted, reactive, ref } from 'vue';

export const MemberChange = defineComponent({
  name: 'MemberChange',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const reduceMember = ref([]);
    const addMember = ref([]);
    const reduceAndAddMember = ref<{ key: string; reduce: any[]; add: any[] }[]>([]);
    const changeList = reactive({
      before: [],
      after: [],
    });
    const isSamePositionChange = ref(false);

    const operInfo = computed(() => {
      return props.viewData.ChangeExtend?.OperInfo;
    });

    const partInfo = computed(() => {
      return props.viewData.Category === 44 ? props.viewData.ChangeExtend : props.viewData.ChangeExtend?.PartInfo;
    });

    const partInfoDescMap = computed(() => {
      return getPartnerChangeDescMap(partInfo.value);
    });

    const empInfo = computed(() => {
      return props.viewData.Category === 46 ? props.viewData.ChangeExtend : props.viewData.ChangeExtend?.EmpInfo;
    });

    const computedOperInfoType = computed(() => {
      if (operInfo.value) {
        return getOperTypeLabelMapper(operInfo.value.C);
      }
      return '';
    });

    /** 仅仅只有主要人员变更的话按职位聚合，否则人员变更大类展示 */
    const isJustMemberChange = computed(() => {
      return reduceMember.value?.length && addMember.value?.length && !(operInfo.value || partInfo.value || empInfo.value?.D?.length);
    });

    const getAmountShow = (key) => {
      const arr = partInfo.value[key];
      return arr.some((el) => el.D || el.E);
    };

    const accAdd = (arg1, arg2) => {
      let r1 = 0;
      let r2 = 0;
      const getArr = (f) => f.toString().split('.');
      if (getArr(arg1).length > 1) {
        r1 = getArr(arg1)[1].length;
      }
      if (getArr(arg2).length > 1) {
        r2 = getArr(arg2)[1].length;
      }

      const m = Math.pow(10, Math.max(r1, r2));
      return (Math.round(arg1 * m) + Math.round(arg2 * m)) / m;
    };

    const computeTotal = (arr) => {
      const getColumn = (list = [], key) => {
        if (key) {
          return list.map((el) => {
            let count = 0;
            if (el[key]) {
              count = parseFloat(el[key]);
            }
            return count;
          });
        }
        return [];
      };
      const before = getColumn(arr, 'B');
      const after = getColumn(arr, 'C');
      const beforeAmount = getColumn(arr, 'D');
      const afterAmount = getColumn(arr, 'E');

      const getNum = (num) => {
        if (num < 0) {
          return 0;
        } else if (num > 100) {
          return 100;
        }
        return num;
      };
      return {
        showFTotal: before.every((el) => el > 0),
        before: `${getNum(before.reduce((pre, cur) => accAdd(pre, cur), 0))}%`,
        after: `${getNum(after.reduce((pre, cur) => accAdd(pre, cur), 0))}%`,
        beforeAmount: beforeAmount.reduce((pre, cur) => accAdd(pre, cur), 0),
        afterAmount: afterAmount.reduce((pre, cur) => accAdd(pre, cur), 0),
      };
    };

    const init = () => {
      if (partInfo.value) {
        try {
          if (partInfo.value?.BP?.A) {
            changeList.before = partInfo.value?.BP?.A;
          }
          if (partInfo.value?.BP?.B) {
            changeList.after = partInfo.value?.BP?.B;
          }
        } catch (e) {
          //
        }
      }

      if (empInfo.value) {
        const data = formatEmployeeData(empInfo.value);
        reduceMember.value = data.reduceMember;
        addMember.value = data.addMember;
        isSamePositionChange.value = data.isSamePositionChange;
        reduceAndAddMember.value = data.reduceAndAddMember;
      }
    };

    onMounted(() => {
      init();
    });

    return {
      partInfo,
      partInfoDescMap,
      operInfo,
      computedOperInfoType,
      isJustMemberChange,
      reduceMember,
      addMember,
      empInfo,
      getAmountShow,
      accAdd,
      computeTotal,
      isSamePositionChange,
      reduceAndAddMember,
      changeList,
      init,
    };
  },
  render() {
    const { viewData } = this;

    // 经营者信息变更渲染函数
    const renderOperInfo = () => {
      if (!this.operInfo) return null;

      return (
        <div>
          <div class="m-b-sm t-main-title">{this.computedOperInfoType}变更</div>
          <table class="ntable">
            <tr>
              <th width="50%" class="tb">
                变更前
              </th>
              <th class="tb">变更后</th>
            </tr>
            <tr>
              <td class="text-center">
                <QEntityLink coyObj={{ KeyNo: this.operInfo.A.K, Name: this.operInfo.A.N }} />
              </td>
              <td class="text-center">
                <QEntityLink coyObj={{ KeyNo: this.operInfo.B.K, Name: this.operInfo.B.N }} />
              </td>
            </tr>
          </table>
        </div>
      );
    };

    // 股东信息变更渲染函数
    const renderPartInfo = () => {
      if (!this.partInfo) return null;

      return (
        <div>
          {/* 股东下降 */}
          {this.partInfo.D && this.partInfo.D.length ? (
            <div>
              <div class="m-b-sm text-dark">
                {this.partInfoDescMap.subTypeName}
                {this.partInfoDescMap.subStockDesc}下降：{this.partInfo.D.length}个<QGlossaryInfo info-id="498" placement="bottomLeft" />
              </div>
              {!this.getAmountShow('D') ? (
                <table class="ntable">
                  <tr>
                    <th width="33.33%" class="tb">
                      {this.partInfoDescMap.typeName}
                    </th>
                    <th width="33.33%" class="tb">
                      变更前
                    </th>
                    <th class="tb">变更后</th>
                  </tr>
                  {this.partInfo.D.map((item, index) => (
                    <tr key={index}>
                      <td class="text-center">
                        <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                      </td>
                      <td class="text-center">
                        {item.B && item.B !== '0%' && item.B !== '0.00%' ? <span>{item.B}</span> : <span>-</span>}
                      </td>
                      <td class="text-center">
                        {item.C && item.C !== '0%' && item.C !== '0.00%' ? <span>{item.C}</span> : <span>-</span>}
                      </td>
                    </tr>
                  ))}
                  {this.partInfo.D.length > 1 ? (
                    <tr>
                      <td>合计</td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.D).before !== '0%' ? this.computeTotal(this.partInfo.D).before : '-'}
                      </td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.D).after !== '0%' ? this.computeTotal(this.partInfo.D).after : '-'}
                      </td>
                    </tr>
                  ) : null}
                </table>
              ) : (
                <table class="ntable ntable-gh">
                  <tr>
                    <th rowspan="2" width="300" class="tb">
                      {this.partInfoDescMap.typeName}
                    </th>
                    <th colspan="2" width="300" class="tb">
                      变更前
                    </th>
                    <th colspan="2" class="tb">
                      变更后
                    </th>
                  </tr>
                  <tr>
                    <th width="150" class="tb">
                      {this.partInfoDescMap.percentTdTitle}
                    </th>
                    <th width="150" class="tb">
                      认缴金额
                    </th>
                    <th width="150" class="tb">
                      {this.partInfoDescMap.percentTdTitle}
                    </th>
                    <th class="tb">认缴金额</th>
                  </tr>
                  {this.partInfo.D.map((item, index) => (
                    <tr key={index}>
                      <td class="text-center">
                        <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                      </td>
                      <td class="text-center">
                        {item.B && item.B !== '0%' && item.B !== '0.00%' ? <span>{item.B}</span> : <span>-</span>}
                      </td>
                      <td class="text-center">{item.D ? <span>{item.D}万元</span> : <span>-</span>}</td>
                      <td class="text-center">
                        {item.C && item.C !== '0%' && item.C !== '0.00%' ? <span>{item.C}</span> : <span>-</span>}
                      </td>
                      <td class="text-center">{item.E ? <span>{item.E}万元</span> : <span>-</span>}</td>
                    </tr>
                  ))}
                  {this.partInfo.D.length > 1 ? (
                    <tr>
                      <td class="text-center">合计</td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.D).before !== '0%' ? this.computeTotal(this.partInfo.D).before : '-'}
                      </td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.D).beforeAmount ? (
                          <span>{this.computeTotal(this.partInfo.D).beforeAmount}万元</span>
                        ) : (
                          <span>-</span>
                        )}
                      </td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.D).after !== '0%' ? this.computeTotal(this.partInfo.D).after : '-'}
                      </td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.D).afterAmount ? (
                          <span>{this.computeTotal(this.partInfo.D).afterAmount}万元</span>
                        ) : (
                          <span>-</span>
                        )}
                      </td>
                    </tr>
                  ) : null}
                </table>
              )}
            </div>
          ) : null}

          {/* 股东上升 */}
          {this.partInfo.H && this.partInfo.H.length ? (
            <div>
              <div class="m-b-sm text-dark">
                {this.partInfoDescMap.subTypeName}
                {this.partInfoDescMap.subStockDesc}上升：{this.partInfo.H.length}个
                {!(this.partInfo.D && this.partInfo.D.length) ? <QGlossaryInfo info-id="498" placement="bottomLeft" /> : null}
              </div>
              {!this.getAmountShow('H') ? (
                <table class="ntable">
                  <tr>
                    <th width="300" class="tb">
                      {this.partInfoDescMap.typeName}
                    </th>
                    <th width="300" class="tb">
                      变更前
                    </th>
                    <th class="tb">变更后</th>
                  </tr>
                  {this.partInfo.H.map((item, index) => (
                    <tr key={index}>
                      <td class="text-center">
                        <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                      </td>
                      <td class="text-center">
                        {item.B && item.B !== '0%' && item.B !== '0.00%' ? <span>{item.B}</span> : <span>-</span>}
                      </td>
                      <td class="text-center">
                        {item.C && item.C !== '0%' && item.C !== '0.00%' ? <span>{item.C}</span> : <span>-</span>}
                      </td>
                    </tr>
                  ))}
                  {this.partInfo.H.length > 1 ? (
                    <tr>
                      <td>合计</td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.H).before !== '0%' ? this.computeTotal(this.partInfo.H).before : '-'}
                      </td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.H).after !== '0%' ? this.computeTotal(this.partInfo.H).after : '-'}
                      </td>
                    </tr>
                  ) : null}
                </table>
              ) : (
                <table class="ntable ntable-gh">
                  <tr>
                    <th rowspan="2" width="300" class="tb">
                      {this.partInfoDescMap.typeName}
                    </th>
                    <th colspan="2" width="300" class="tb">
                      变更前
                    </th>
                    <th colspan="2" class="tb">
                      变更后
                    </th>
                  </tr>
                  <tr>
                    <th width="150" class="tb">
                      {this.partInfoDescMap.percentTdTitle}
                    </th>
                    <th width="150" class="tb">
                      认缴金额
                    </th>
                    <th width="150" class="tb">
                      {this.partInfoDescMap.percentTdTitle}
                    </th>
                    <th class="tb">认缴金额</th>
                  </tr>
                  {this.partInfo.H.map((item, index) => (
                    <tr key={index}>
                      <td class="text-center">
                        <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                      </td>
                      <td class="text-center">
                        {item.B && item.B !== '0%' && item.B !== '0.00%' ? <span>{item.B}</span> : <span>-</span>}
                      </td>
                      <td class="text-center">{item.D ? <span>{item.D}万元</span> : <span>-</span>}</td>
                      <td class="text-center">
                        {item.C && item.C !== '0%' && item.C !== '0.00%' ? <span>{item.C}</span> : <span>-</span>}
                      </td>
                      <td class="text-center">{item.E ? <span>{item.E}万元</span> : <span>-</span>}</td>
                    </tr>
                  ))}
                  {this.partInfo.H.length > 1 ? (
                    <tr>
                      <td>合计</td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.H).before !== '0%' ? this.computeTotal(this.partInfo.H).before : '-'}
                      </td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.H).beforeAmount ? (
                          <span>{this.computeTotal(this.partInfo.H).beforeAmount}万元</span>
                        ) : (
                          <span>-</span>
                        )}
                      </td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.H).after !== '0%' ? this.computeTotal(this.partInfo.H).after : '-'}
                      </td>
                      <td class="text-center">
                        {this.computeTotal(this.partInfo.H).afterAmount ? (
                          <span>{this.computeTotal(this.partInfo.H).afterAmount}万元</span>
                        ) : (
                          <span>-</span>
                        )}
                      </td>
                    </tr>
                  ) : null}
                </table>
              )}
            </div>
          ) : null}

          {/* 股东退出 */}
          {this.partInfo.E && this.partInfo.E.length ? (
            <div>
              <div class="m-b-sm text-dark">
                {this.partInfoDescMap.typeName}退出：{this.partInfo.E.length}个
              </div>
              <table class="ntable">
                <tr>
                  <th width="50%" class="tb">
                    {this.partInfoDescMap.typeName}
                  </th>
                  <th class="tb">退出前({this.partInfoDescMap.percentTdTitle})</th>
                </tr>
                {this.partInfo.E.map((item, index) => (
                  <tr key={index}>
                    <td class="text-center">
                      <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                    </td>
                    <td class="text-center">{item.B && item.B !== '0%' && item.B !== '0.00%' ? <span>{item.B}</span> : <span>-</span>}</td>
                  </tr>
                ))}
              </table>
            </div>
          ) : null}

          {/* 股东新增 */}
          {this.partInfo.F && this.partInfo.F.length ? (
            <div>
              <div class="m-b-sm text-dark">
                {this.partInfoDescMap.typeName}新增：{this.partInfo.F.length}个
              </div>
              <table class="ntable">
                <tr>
                  <th width="50%" class="tb">
                    {this.partInfoDescMap.typeName}
                  </th>
                  <th class="tb">{this.partInfoDescMap.percentTdTitle}</th>
                </tr>
                {this.partInfo.F.map((item, index) => (
                  <tr key={index}>
                    <td class="text-center">
                      <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                    </td>
                    <td class="text-center">{item.B && item.B !== '0%' && item.B !== '0.00%' ? <span>{item.B}</span> : <span>-</span>}</td>
                  </tr>
                ))}
                {this.partInfo.F.length > 1 && this.computeTotal(this.partInfo.F).showFTotal ? (
                  <tr>
                    <td>合计</td>
                    <td class="text-center">{this.computeTotal(this.partInfo.F).before}</td>
                  </tr>
                ) : null}
              </table>
            </div>
          ) : null}

          {/* 大股东变更 */}
          {this.changeList.before.length || this.changeList.after.length ? (
            <div>
              <div class="m-b-sm text-dark">{this.partInfoDescMap.majorShareHolderTypeName}变更</div>
              <table class="ntable">
                {this.changeList.before.length && this.changeList.after.length ? (
                  [
                    <tr>
                      <td width="50%" class="tb">
                        变更前
                      </td>
                      <td>
                        {this.changeList.before.map((el: any, i) => (
                          <span key={`b${i}`}>
                            <QEntityLink coyObj={el} />
                            {el.StockPercent ? (
                              <span>
                                ，变更前{this.partInfoDescMap.majorShareHolderPercentDesc}
                                {el.StockPercent} <br />
                              </span>
                            ) : null}
                          </span>
                        ))}
                      </td>
                    </tr>,
                    <tr>
                      <td width="50%" class="tb">
                        变更后
                      </td>
                      {this.changeList.before.length ? (
                        <td>
                          {this.changeList.after.map((el: any, i) => (
                            <span key={`a${i}`}>
                              <QEntityLink coyObj={el} />
                              {el.StockPercent ? (
                                <span>
                                  ，{this.partInfoDescMap.majorShareHolderPercentDesc}
                                  {el.StockPercent} <br />
                                </span>
                              ) : null}
                            </span>
                          ))}
                        </td>
                      ) : (
                        <td>-</td>
                      )}
                    </tr>,
                  ]
                ) : (
                  <tr>
                    <td width="50%" class="tb">
                      变更后
                    </td>
                    <td>
                      {this.changeList.before.length ? <QEntityLink coy-arr={this.changeList.before} sep="、" /> : null}
                      {this.changeList.after.length ? <QEntityLink coy-arr={this.changeList.after} sep="、" /> : null}
                      {this.changeList.before.length ? '不再是' : '成为'}
                      <QEntityLink coyObj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }} />的
                      {this.partInfoDescMap.majorShareHolderTypeName}
                    </td>
                  </tr>
                )}
              </table>
            </div>
          ) : null}
        </div>
      );
    };

    // 员工信息变更渲染函数
    const renderEmpInfo = () => {
      if (!this.empInfo) return null;

      if (this.isSamePositionChange) {
        return (
          <div>
            {/* 职务变更 */}
            {this.empInfo.D && this.empInfo.D.length ? (
              <div>
                <div class="m-b-xs t-main-title">职务变更</div>
                <table class="ntable">
                  <tr>
                    <th width="33.33%" class="tb">
                      主要成员
                    </th>
                    <th width="33.33%" class="tb">
                      变更前
                    </th>
                    <th width="33.33%" class="tb">
                      变更后
                    </th>
                  </tr>
                  {this.empInfo.D.map((item, index) => (
                    <tr key={index}>
                      <td class="text-center">
                        <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                      </td>
                      <td class="text-center">{item.B || '-'}</td>
                      <td class="text-center">{item.C || '-'}</td>
                    </tr>
                  ))}
                </table>
              </div>
            ) : null}

            {this.isJustMemberChange ? (
              this.reduceAndAddMember.map((item) => (
                <div key={`${item.key}_container`}>
                  <div class="m-b-xs t-main-title">{item.key}变更</div>
                  <table class="ntable">
                    <tr>
                      <th class="tb" width="50%">
                        退出
                      </th>
                      <th class="tb" width="50%">
                        新增
                      </th>
                    </tr>
                    <tr>
                      <td class="text-center">
                        <QEntityLink coy-arr={item.reduce} />
                      </td>
                      <td class="text-center">
                        <QEntityLink coy-arr={item.add} />
                      </td>
                    </tr>
                  </table>
                </div>
              ))
            ) : (
              <div>
                <div class="m-b-xs t-main-title">人员变更</div>
                <table class="ntable">
                  <tr>
                    <th class="tb" width="33.33%">
                      职务类型
                    </th>
                    <th class="tb" width="33.33%">
                      退出
                    </th>
                    <th class="tb" width="33.33%">
                      新增
                    </th>
                  </tr>
                  {this.reduceAndAddMember.map((item) => (
                    <tr key={item.key}>
                      <td class="text-center">{item.key}</td>
                      <td class="text-center">
                        <QEntityLink coy-arr={item.reduce} />
                      </td>
                      <td class="text-center">
                        <QEntityLink coy-arr={item.add} />
                      </td>
                    </tr>
                  ))}
                </table>
              </div>
            )}
          </div>
        );
      }

      return (
        <div>
          <div class="m-b-xs t-main-title">主要成员变更</div>

          {/* 职务变更 */}
          {this.empInfo.D && this.empInfo.D.length ? (
            <div>
              <div class="m-b-sm text-dark">职务变更</div>
              <table class="ntable">
                <tr>
                  <th width="33.33%" class="tb">
                    主要成员
                  </th>
                  <th width="33.33%" class="tb">
                    变更前
                  </th>
                  <th class="tb">变更后</th>
                </tr>
                {this.empInfo.D.map((item, index) => (
                  <tr key={index}>
                    <td class="text-center">
                      <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                    </td>
                    <td class="text-center">{item.B || '-'}</td>
                    <td class="text-center">{item.C || '-'}</td>
                  </tr>
                ))}
              </table>
            </div>
          ) : null}

          {/* 退出 */}
          {this.empInfo.E && this.empInfo.E.length ? (
            <div>
              <div class="m-b-sm text-dark">退出</div>
              <table class="ntable">
                <tr>
                  <th width="50%" class="tb">
                    主要成员
                  </th>
                  <th class="tb">退出前职务</th>
                </tr>
                {this.empInfo.E.map((item, index) => (
                  <tr key={index}>
                    <td class="text-center">
                      <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                    </td>
                    <td class="text-center">{item.B || '-'}</td>
                  </tr>
                ))}
              </table>
            </div>
          ) : null}

          {/* 新增 */}
          {this.empInfo.F && this.empInfo.F.length ? (
            <div>
              <div class="m-b-sm text-dark">新增</div>
              <table class="ntable">
                <tr>
                  <th width="50%" class="tb">
                    主要成员
                  </th>
                  <th class="tb">职务</th>
                </tr>
                {this.empInfo.F.map((item, index) => (
                  <tr key={index}>
                    <td class="text-center">
                      <QEntityLink coyObj={{ KeyNo: item.K, Name: item.A }} />
                    </td>
                    <td class="text-center">{item.B || '-'}</td>
                  </tr>
                ))}
              </table>
            </div>
          ) : null}
        </div>
      );
    };

    const renderTypeCom = () => {
      return (
        <div>
          <table class="ntable">
            <tr>
              <td width="120" class="tb">
                变更企业
              </td>
              <td width="360">
                {viewData.KeyNo ? (
                  <QEntityLink coyObj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }} />
                ) : (
                  <span>{viewData.Name || '-'}</span>
                )}
              </td>
              <td width="120" class="tb">
                更新时间
                <QGlossaryInfo info-id="241" />
              </td>
              <td>{dateFormat(viewData.ChangeDate)}</td>
            </tr>
          </table>
          {renderOperInfo()}
          {renderPartInfo()}
          {renderEmpInfo()}
        </div>
      );
    };

    const renderTypePer = () => {};

    return (
      <div class="member-change-content">
        {viewData.type === 'com' ? renderTypeCom() : null}
        {viewData.type === 'per' ? renderTypePer() : null}
      </div>
    );
  },
});
