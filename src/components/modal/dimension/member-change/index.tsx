import QEntityLink from '@/components/global/q-entity-link';
import { formatEmployeeData, getOperTypeLabelMapper, getPartnerChangeDescMap } from '@/utils/firm';
import { dateFormat } from '@/utils/format';
import { computed, defineComponent, reactive, ref } from 'vue';

export const MemberChange = defineComponent({
  name: 'MemberChange',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const reduceMember = ref([]);
    const addMember = ref([]);
    const reduceAndAddMember = ref<{ key: string; reduce: any[]; add: any[] }[]>([]);
    const changeList = reactive({
      before: [],
      after: [],
    });
    const isSamePositionChange = ref(false);

    const operInfo = computed(() => {
      return props.viewData.ChangeExtend?.OperInfo;
    });

    const partInfo = computed(() => {
      return props.viewData.Category === 44 ? props.viewData.ChangeExtend : props.viewData.ChangeExtend?.PartInfo;
    });

    const partInfoDescMap = computed(() => {
      return getPartnerChangeDescMap(partInfo.value);
    });

    const empInfo = computed(() => {
      return props.viewData.Category === 46 ? props.viewData.ChangeExtend : props.viewData.ChangeExtend?.EmpInfo;
    });

    const computedOperInfoType = computed(() => {
      if (operInfo.value) {
        return getOperTypeLabelMapper(operInfo.value.C);
      }
      return '';
    });

    /** 仅仅只有主要人员变更的话按职位聚合，否则人员变更大类展示 */
    const isJustMemberChange = computed(() => {
      return reduceMember.value?.length && addMember.value?.length && !(operInfo.value || partInfo.value || empInfo.value?.D?.length);
    });

    const getAmountShow = (key) => {
      const arr = partInfo.value[key];
      return arr.some((el) => el.D || el.E);
    };

    const accAdd = (arg1, arg2) => {
      let r1 = 0;
      let r2 = 0;
      const getArr = (f) => f.toString().split('.');
      if (getArr(arg1).length > 1) {
        r1 = getArr(arg1)[1].length;
      }
      if (getArr(arg2).length > 1) {
        r2 = getArr(arg2)[1].length;
      }

      const m = Math.pow(10, Math.max(r1, r2));
      return (Math.round(arg1 * m) + Math.round(arg2 * m)) / m;
    };

    const computeTotal = (arr) => {
      const getColumn = (list = [], key) => {
        if (key) {
          return list.map((el) => {
            let count = 0;
            if (el[key]) {
              count = parseFloat(el[key]);
            }
            return count;
          });
        }
        return [];
      };
      const before = getColumn(arr, 'B');
      const after = getColumn(arr, 'C');
      const beforeAmount = getColumn(arr, 'D');
      const afterAmount = getColumn(arr, 'E');

      const getNum = (num) => {
        if (num < 0) {
          return 0;
        } else if (num > 100) {
          return 100;
        }
        return num;
      };
      return {
        showFTotal: before.every((el) => el > 0),
        before: `${getNum(before.reduce((pre, cur) => accAdd(pre, cur), 0))}%`,
        after: `${getNum(after.reduce((pre, cur) => accAdd(pre, cur), 0))}%`,
        beforeAmount: beforeAmount.reduce((pre, cur) => accAdd(pre, cur), 0),
        afterAmount: afterAmount.reduce((pre, cur) => accAdd(pre, cur), 0),
      };
    };

    const init = () => {
      if (partInfo.value) {
        try {
          if (partInfo.value?.BP?.A) {
            changeList.before = partInfo.value?.BP?.A;
          }
          if (partInfo.value?.BP?.B) {
            changeList.after = partInfo.value?.BP?.B;
          }
        } catch (e) {
          //
        }
      }

      if (empInfo.value) {
        const data = formatEmployeeData(empInfo.value);
        reduceMember.value = data.reduceMember;
        addMember.value = data.addMember;
        isSamePositionChange.value = data.isSamePositionChange;
        reduceAndAddMember.value = data.reduceAndAddMember;
      }
    };

    return {
      partInfo,
      partInfoDescMap,
      operInfo,
      computedOperInfoType,
      isJustMemberChange,
      reduceMember,
      addMember,
      empInfo,
      getAmountShow,
      accAdd,
      computeTotal,
      isSamePositionChange,
      reduceAndAddMember,
      changeList,
      init,
    };
  },
  render() {
    const { viewData } = this;

    const renderTypeCom = () => {
      return (
        <table class="ntable">
          <tr>
            <td width="120" class="tb">
              变更企业
            </td>
            <td width="360">
              {viewData.KeyNo ? (
                <QEntityLink coy-obj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }} />
              ) : (
                <span>{viewData.Name || '-'}</span>
              )}
            </td>
            <td width="120" class="tb">
              更新时间
              <app-glossary-info info-id="241" />
            </td>
            <td>{dateFormat(viewData.ChangeDate)}</td>
          </tr>
        </table>
      );
    };

    const renderTypePer = () => {};

    return (
      <div>
        {viewData.type === 'com' ? renderTypeCom() : null}
        {viewData.type === 'per' ? renderTypePer() : null}
      </div>
    );
  },
});
