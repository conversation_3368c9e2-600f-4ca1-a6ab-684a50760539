<template>
  <div class="member-change-content">
    <template v-if="viewData.type==='com'">
      <table class="ntable">
        <tr>
          <td width="120" class="tb">变更企业</td>
          <td width="360">
            <app-coy v-if="viewData.KeyNo" :coy-obj="{KeyNo: viewData.KeyNo, Name: viewData.Name}" />
            <span v-else>{{ viewData.Name || '-' }}</span>
          </td>
          <td width="120" class="tb">
            更新时间<app-glossary-info info-id="241" />
          </td>
          <td>
            {{ viewData.ChangeDate | dateformat }}
          </td>
        </tr>
      </table>
      <template v-if="operInfo">
        <div class="m-b-sm t-main-title">{{ computedOperInfoType }}变更</div>
        <table class="ntable">
          <tr>
            <th width="50%" class="tb">变更前</th>
            <th class="tb">变更后</th>
          </tr>
          <tr>
            <td class="text-center">
              <app-coy :coy-obj="{ KeyNo: operInfo.A.K, Name: operInfo.A.N }" />
            </td>
            <td class="text-center">
              <app-coy :coy-obj="{ KeyNo: operInfo.B.K, Name: operInfo.B.N }" />
            </td>
          </tr>
        </table>
      </template>
      <template v-if="partInfo">
        <div class="m-b-xs t-main-title">{{ partInfoDescMap.typeName }}{{ partInfoDescMap.stockDesc }}变更</div>
        <template v-if="partInfo.D && partInfo.D.length">
          <div class="m-b-sm text-dark">{{ partInfoDescMap.subTypeName }}{{ partInfoDescMap.subStockDesc }}下降：{{ partInfo.D.length }}个<app-glossary-info info-id="498" placement="bottomLeft" /></div>
          <table v-if="!getAmountShow('D')" class="ntable">
            <tr>
              <th width="33.33%" class="tb">{{ partInfoDescMap.typeName }}</th>
              <th width="33.33%" class="tb">变更前</th>
              <th class="tb">变更后</th>
            </tr>
            <tr v-for="(item, index) in partInfo.D" :key="index">
              <td class="text-center">
                <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
              </td>
              <td class="text-center">
                <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'">{{ item.C }}</span>
                <span v-else>-</span>
              </td>
            </tr>
            <tr v-if="partInfo.D.length > 1">
              <td>合计</td>
              <td class="text-center">
                {{ computeTotal(partInfo.D).before !== '0%' ? computeTotal(partInfo.D).before : '-' }}
              </td>
              <td class="text-center">
                {{ computeTotal(partInfo.D).after !== '0%' ? computeTotal(partInfo.D).after : '-' }}
              </td>
            </tr>
          </table>
          <table v-else class="ntable ntable-gh">
            <tr>
              <th rowspan="2" width="300" class="tb">{{ partInfoDescMap.typeName }}</th>
              <th colspan="2" width="300" class="tb">变更前</th>
              <th colspan="2" class="tb">变更后</th>
            </tr>
            <tr>
              <th width="150" class="tb">{{ partInfoDescMap.percentTdTitle }}</th>
              <th width="150" class="tb">认缴金额</th>
              <th width="150" class="tb">{{ partInfoDescMap.percentTdTitle }}</th>
              <th class="tb">认缴金额</th>
            </tr>
            <tr v-for="(item, index) in partInfo.D" :key="index">
              <td class="text-center">
                <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
              </td>
              <td class="text-center">
                <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <span v-if="item.D">{{ item.D }}万元</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'">{{ item.C }}</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <span v-if="item.E">{{ item.E }}万元</span>
                <span v-else>-</span>
              </td>
            </tr>
            <tr v-if="partInfo.D.length > 1">
              <td class="text-center">合计</td>
              <td class="text-center">
                {{ computeTotal(partInfo.D).before !== '0%' ? computeTotal(partInfo.D).before : '-' }}
              </td>
              <td class="text-center">
                <span v-if="computeTotal(partInfo.D).beforeAmount">{{ computeTotal(partInfo.D).beforeAmount }}万元</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                {{ computeTotal(partInfo.D).after !== '0%' ? computeTotal(partInfo.D).after : '-' }}
              </td>
              <td class="text-center">
                <span v-if="computeTotal(partInfo.D).afterAmount">{{ computeTotal(partInfo.D).afterAmount }}万元</span>
                <span v-else>-</span>
              </td>
            </tr>
          </table>
        </template>
        <template v-if="partInfo.H && partInfo.H.length">
          <div class="m-b-sm text-dark">{{ partInfoDescMap.subTypeName }}{{ partInfoDescMap.subStockDesc }}上升：{{ partInfo.H.length }}个<app-glossary-info v-if="!(partInfo.D && partInfo.D.length)" info-id="498" placement="bottomLeft" /></div>
          <table v-if="!getAmountShow('H')" class="ntable">
            <tr>
              <th width="300" class="tb">{{ partInfoDescMap.typeName }}</th>
              <th width="300" class="tb">变更前</th>
              <th class="tb">变更后</th>
            </tr>
            <tr v-for="(item, index) in partInfo.H" :key="index">
              <td class="text-center">
                <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
              </td>
              <td class="text-center">
                <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'">{{ item.C }}</span>
                <span v-else>-</span>
              </td>
            </tr>
            <tr v-if="partInfo.H.length > 1">
              <td>合计</td>
              <td class="text-center">
                {{ computeTotal(partInfo.H).before !== '0%' ? computeTotal(partInfo.H).before : '-' }}
              </td>
              <td class="text-center">
                {{ computeTotal(partInfo.H).after !== '0%' ? computeTotal(partInfo.H).after : '-' }}
              </td>
            </tr>
          </table>
          <table v-else class="ntable ntable-gh">
            <tr>
              <th rowspan="2" width="300" class="tb">{{ partInfoDescMap.typeName }}</th>
              <th colspan="2" width="300" class="tb">变更前</th>
              <th colspan="2" class="tb">变更后</th>
            </tr>
            <tr>
              <th width="150" class="tb">{{ partInfoDescMap.percentTdTitle }}</th>
              <th width="150" class="tb">认缴金额</th>
              <th width="150" class="tb">{{ partInfoDescMap.percentTdTitle }}</th>
              <th class="tb">认缴金额</th>
            </tr>
            <tr v-for="(item, index) in partInfo.H" :key="index">
              <td class="text-center">
                <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
              </td>
              <td class="text-center">
                <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <span v-if="item.D">{{ item.D }}万元</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <span v-if="item.C && item.C !== '0%' && item.C !== '0.00%'">{{ item.C }}</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <span v-if="item.E">{{ item.E }}万元</span>
                <span v-else>-</span>
              </td>
            </tr>
            <tr v-if="partInfo.H.length > 1">
              <td>合计</td>
              <td class="text-center">
                {{ computeTotal(partInfo.H).before !== '0%' ? computeTotal(partInfo.H).before : '-' }}
              </td>
              <td class="text-center">
                <span v-if="computeTotal(partInfo.H).beforeAmount">{{ computeTotal(partInfo.H).beforeAmount }}万元</span>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                {{ computeTotal(partInfo.H).after !== '0%' ? computeTotal(partInfo.H).after : '-' }}
              </td>
              <td class="text-center">
                <span v-if="computeTotal(partInfo.H).afterAmount">{{ computeTotal(partInfo.H).afterAmount }}万元</span>
                <span v-else>-</span>
              </td>
            </tr>
          </table>
        </template>
        <template v-if="partInfo.E && partInfo.E.length">
          <div class="m-b-sm text-dark">{{ partInfoDescMap.typeName }}退出：{{ partInfo.E.length }}个</div>
          <table class="ntable">
            <tr>
              <th width="50%" class="tb">{{ partInfoDescMap.typeName }}</th>
              <th class="tb">退出前({{ partInfoDescMap.percentTdTitle }})</th>
            </tr>
            <tr v-for="(item, index) in partInfo.E" :key="index">
              <td class="text-center">
                <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
              </td>
              <td class="text-center">
                <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
                <span v-else>-</span>
              </td>
            </tr>
          </table>
        </template>
        <template v-if="partInfo.F && partInfo.F.length">
          <div class="m-b-sm text-dark">{{ partInfoDescMap.typeName }}新增：{{ partInfo.F.length }}个</div>
          <table class="ntable">
            <tr>
              <th width="50%" class="tb">{{ partInfoDescMap.typeName }}</th>
              <th class="tb">{{ partInfoDescMap.percentTdTitle }}</th>
            </tr>
            <tr v-for="(item, index) in partInfo.F" :key="index">
              <td class="text-center">
                <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
              </td>
              <td class="text-center">
                <span v-if="item.B && item.B !== '0%' && item.B !== '0.00%'">{{ item.B }}</span>
                <span v-else>-</span>
              </td>
            </tr>
            <tr v-if="partInfo.F.length > 1 && computeTotal(partInfo.F).showFTotal">
              <td>合计</td>
              <td class="text-center">{{ computeTotal(partInfo.F).before }}</td>
            </tr>
          </table>
        </template>
        <!-- 大股东变更 -->
        <template v-if="changeList.before.length || changeList.after.length">
          <div class="m-b-sm text-dark">{{ partInfoDescMap.majorShareHolderTypeName }}变更</div>
          <table class="ntable">
            <template v-if="changeList.before.length && changeList.after.length">
              <tr>
                <td width="50%" class="tb">变更前</td>
                <td>
                  <template v-for="(el, i) in changeList.before">
                    <app-coy :key="`b${i}`" :coy-obj="el" />
                    <template v-if="el.StockPercent">
                      ，变更前{{ partInfoDescMap.majorShareHolderPercentDesc }}{{ el.StockPercent }} <br :key="`e1${i}`">
                    </template>
                  </template>
                </td>
              </tr>
              <tr>
                <td width="50%" class="tb">变更后</td>
                <td v-if="changeList.before.length">
                  <template v-for="(el, i) in changeList.after">
                    <app-coy :key="`a${i}`" :coy-obj="el" />
                    <template v-if="el.StockPercent">
                      ，{{ partInfoDescMap.majorShareHolderPercentDesc }}{{ el.StockPercent }} <br :key="`e1${i}`">
                    </template>
                  </template>
                </td>
                <td v-else>-</td>
              </tr>
            </template>
            <template v-else>
              <tr>
                <td width="50%" class="tb">变更后</td>
                <td>
                  <app-coy v-if="changeList.before.length" :coy-arr="changeList.before" sep="、" />
                  <app-coy v-if="changeList.after.length" :coy-arr="changeList.after" sep="、" />
                  {{ changeList.before.length ? '不再是' : '成为' }}
                  <app-coy :coy-obj="{KeyNo: viewData.KeyNo, Name: viewData.Name}" />
                  的{{ partInfoDescMap.majorShareHolderTypeName }}
                </td>
              </tr>
            </template>
          </table>
        </template>

      </template>
      <!-- 职务变更、主要成员新增、主要成员退出 -->
      <template v-if="empInfo">
        <template v-if="isSamePositionChange">
          <template v-if="empInfo.D && empInfo.D.length">
            <div class="m-b-xs t-main-title">职务变更</div>
            <table class="ntable">
              <tr>
                <th width="33.33%" class="tb">主要成员</th>
                <th width="33.33%" class="tb">变更前</th>
                <th width="33.33%" class="tb">变更后</th>
              </tr>
              <tr v-for="(item, index) in empInfo.D" :key="index">
                <td class="text-center">
                  <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
                </td>
                <td class="text-center">{{ item.B || '-' }}</td>
                <td class="text-center">{{ item.C || '-' }}</td>
              </tr>
            </table>
          </template>
          <template v-if="isJustMemberChange">
            <template v-for="item in reduceAndAddMember">
              <div :key="`${item.key}_title`" class="m-b-xs t-main-title">{{ item.key }}变更</div>
              <table :key="`${item.key}_table`" class="ntable">
                <tr>
                  <th class="tb" width="50%">退出</th>
                  <th class="tb" width="50%">新增</th>
                </tr>
                <tr>
                  <td class="text-center">
                    <app-coy :coy-arr="item.reduce" />
                  </td>
                  <td class="text-center">
                    <app-coy :coy-arr="item.add" />
                  </td>
                </tr>
              </table>
            </template>
          </template>
          <template v-else>
            <div class="m-b-xs t-main-title">人员变更</div>
            <table class="ntable">
              <tr>
                <th class="tb" width="33.33%">职务类型</th>
                <th class="tb" width="33.33%">退出</th>
                <th class="tb" width="33.33%">新增</th>
              </tr>
              <tr v-for="item in reduceAndAddMember" :key="item.key">
                <td class="text-center">
                  {{ item.key }}
                </td>
                <td class="text-center">
                  <app-coy :coy-arr="item.reduce" />
                </td>
                <td class="text-center">
                  <app-coy :coy-arr="item.add" />
                </td>
              </tr>
            </table>
          </template>
        </template>
        <template v-else>
          <div class="m-b-xs t-main-title">主要成员变更</div>
          <template v-if="empInfo.D && empInfo.D.length">
            <div class="m-b-sm text-dark">职务变更</div>
            <table class="ntable">
              <tr>
                <th width="33.33%" class="tb">主要成员</th>
                <th width="33.33%" class="tb">变更前</th>
                <th class="tb">变更后</th>
              </tr>
              <tr v-for="(item, index) in empInfo.D" :key="index">
                <td class="text-center">
                  <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
                </td>
                <td class="text-center">{{ item.B || '-' }}</td>
                <td class="text-center">{{ item.C || '-' }}</td>
              </tr>
            </table>
          </template>
          <template v-if="empInfo.E && empInfo.E.length">
            <div class="m-b-sm text-dark">退出</div>
            <table class="ntable">
              <tr>
                <th width="50%" class="tb">主要成员</th>
                <th class="tb">退出前职务</th>
              </tr>
              <tr v-for="(item, index) in empInfo.E" :key="index">
                <td class="text-center">
                  <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
                </td>
                <td class="text-center">
                  {{ item.B || '-' }}
                </td>
              </tr>
            </table>
          </template>
          <template v-if="empInfo.F && empInfo.F.length">
            <div class="m-b-sm text-dark">新增</div>
            <table class="ntable">
              <tr>
                <th width="50%" class="tb">主要成员</th>
                <th class="tb">职务</th>
              </tr>
              <tr v-for="(item, index) in empInfo.F" :key="index">
                <td class="text-center">
                  <app-coy :coy-obj="{KeyNo: item.K, Name: item.A}" />
                </td>
                <td class="text-center">
                  {{ item.B || '-' }}
                </td>
              </tr>
            </table>
          </template>
        </template>
      </template>
    </template>
    <table v-if="viewData.type==='per'" class="ntable">
      <tr>
        <td class="tb" width="23%">{{ viewData.type==='per'?'变更人员':'变更企业' }}</td>
        <td width="27%"><app-coy :coy-obj="{ Name: viewData.Name, KeyNo: viewData.KeyNo, Org: '' }" /></td>
        <td class="tb" width="23%">
          更新时间<app-glossary-info info-id="241" />
        </td>
        <td width="27%">{{ viewData.ChangeDate | dateformat }}</td>
      </tr>
      <template v-if="viewData.type==='per'">
        <template v-if="operInfo">
          <tr>
            <td colspan="4">{{ computedOperInfoType }}变更</td>
          </tr>
          <tr>
            <td class="tb">变更内容</td>
            <td colspan="3">
              <span>不再担任</span>
              <template v-if="operInfo">
                <a v-if="operInfo.K" :href="`/firm/${operInfo.K}.html`" target="_blank">{{ operInfo.A || '-' }}</a>
                <span v-else>{{ operInfo.A || '-' }}</span>
                <span>的{{ computedOperInfoType }}</span>
              </template>
            </td>
          </tr>
        </template>
        <template v-if="viewData.ChangeExtend.PartInfo">
          <tr>
            <td colspan="4">撤出对外投资</td>
          </tr>
          <tr>
            <td class="tb">变更内容</td>
            <td colspan="3">
              <span>退出投资企业</span>
              <a v-if="viewData.ChangeExtend.PartInfo.K" :href="`/firm/${viewData.ChangeExtend.PartInfo.K}.html`" target="_blank">{{ viewData.ChangeExtend.PartInfo.A || '-' }}</a>
              <span v-else>{{ viewData.ChangeExtend.PartInfo.A || '-' }}</span>
              <span v-if="viewData.ChangeExtend.PartInfo.B">，退出前持股比例：{{ viewData.ChangeExtend.PartInfo.B }}</span>
            </td>
          </tr>
          <!-- 投资变更 -->
          <tr v-if="viewData.Desc.RelateChange">
            <td class="tb">关联变更</td>
            <td colspan="3">
              {{ viewData.Desc.RelateChange }}
            </td>
          </tr>
        </template>
        <template v-if="viewData.ChangeExtend.EmpInfo">
          <tr>
            <td colspan="4">主要成员变更</td>
          </tr>
          <tr>
            <td class="tb">变更内容</td>
            <td colspan="3">
              <span>退出企业</span>
              <a v-if="viewData.ChangeExtend.EmpInfo.K" :href="`/firm/${viewData.ChangeExtend.EmpInfo.K}.html`" target="_blank">{{ viewData.ChangeExtend.EmpInfo.A || '-' }}</a>
              <span v-else>{{ viewData.ChangeExtend.EmpInfo.A || '-' }}</span>
              <span v-if="viewData.ChangeExtend.EmpInfo.B">，退出前职务：{{ viewData.ChangeExtend.EmpInfo.B }}</span>
            </td>
          </tr>
        </template>
      </template>
    </table>
  </div>
</template>

<script>
import appRiskUtils from '@/components/app-modal/app-risk/utils'
import { getOperType } from '@/utils/company'
export default {
  name: 'MemberChangeDetail',

  props: {
    viewData: {
      type: Object,

      default () {
        return {}
      }
    }
  },

  data () {
    return {
      changeList: {
        before: [],
        after: []
      },
      addMember: [],
      reduceMember: [],
      // 是否包含相同职位的主要人员变更
      isSamePositionChange: false,
      // 如果包含，则需要按职位聚合变更内容
      reduceAndAddMember: []
    }
  },

  computed: {
    operInfo () {
      return this.viewData.ChangeExtend?.OperInfo
    },

    partInfo () {
      return this.viewData.Category === 44 ? this.viewData.ChangeExtend : this.viewData.ChangeExtend?.PartInfo
    },

    partInfoDescMap () {
      return appRiskUtils.getPartnerChangeDescMap(this.partInfo)
    },

    empInfo () {
      return this.viewData.Category === 46 ? this.viewData.ChangeExtend : this.viewData.ChangeExtend?.EmpInfo
    },

    computedOperInfoType () {
      if (this.operInfo) {
        return getOperType(this.operInfo.C)
      }
      return ''
    },

    /** 仅仅只有主要人员变更的话按职位聚合，否则人员变更大类展示 */
    isJustMemberChange () {
      return this.reduceMember?.length && this.addMember?.length && !(this.operInfo || this.partInfo || this.empInfo?.D?.length)
    }

  },

  created () {
    if (this.partInfo) {
      try {
        if (this.partInfo?.BP?.A) {
          this.changeList.before = this.partInfo?.BP?.A
        }
        if (this.partInfo?.BP?.B) {
          this.changeList.after = this.partInfo?.BP?.B
        }
      } catch (e) {}
    }

    if (this.empInfo) {
      const {
        reduceMember,
        addMember,
        isSamePositionChange,
        reduceAndAddMember
      } = appRiskUtils.formatEmployeeData(this.empInfo)
      this.reduceMember = reduceMember
      this.addMember = addMember
      this.isSamePositionChange = isSamePositionChange
      this.reduceAndAddMember = reduceAndAddMember
    }
  },

  methods: {
    getAmountShow (key) {
      const arr = this.partInfo[key]
      return arr.some(el => el.D || el.E)
    },

    accAdd (arg1, arg2) {
      let r1 = 0
      let r2 = 0
      const getArr = (f) => f.toString().split('.')
      if (getArr(arg1).length > 1) {
        r1 = getArr(arg1)[1].length
      }
      if (getArr(arg2).length > 1) {
        r2 = getArr(arg2)[1].length
      }

      const m = Math.pow(10, Math.max(r1, r2))
      return (Math.round(arg1 * m) + Math.round(arg2 * m)) / m
    },

    computeTotal (arr) {
      const getColumn = (list = [], key) => {
        if (key) {
          return list.map(el => {
            let count = 0
            if (el[key]) {
              count = parseFloat(el[key])
            }
            return count
          })
        }
        return []
      }
      const before = getColumn(arr, 'B')
      const after = getColumn(arr, 'C')
      const beforeAmount = getColumn(arr, 'D')
      const afterAmount = getColumn(arr, 'E')

      const getNum = (num) => {
        if (num < 0) {
          return 0
        } else if (num > 100) {
          return 100
        }
        return num
      }
      return {
        showFTotal: before.every(el => el > 0),
        before: `${getNum(before.reduce((pre, cur) => this.accAdd(pre, cur), 0))}%`,
        after: `${getNum(after.reduce((pre, cur) => this.accAdd(pre, cur), 0))}%`,
        beforeAmount: beforeAmount.reduce((pre, cur) => this.accAdd(pre, cur), 0),
        afterAmount: afterAmount.reduce((pre, cur) => this.accAdd(pre, cur), 0)
      }
    }
  }
}
</script>

<style lang="scss" src="../style.scss" scoped></style>

<style lang="scss">
.member-change-content {
  .ntable  + .t-main-title{
    margin-top: 30px;
  }

  .t-main-title {
    color: #333;
    font-weight: bold;
  }

  .m-b-sm {
    .app-glossary-info {
      top: 3px;
    }
  }

  .ntable-gh {
    th:not(:last-child){
      border-color:#E4EEF6;
    }
  }
}
</style>
