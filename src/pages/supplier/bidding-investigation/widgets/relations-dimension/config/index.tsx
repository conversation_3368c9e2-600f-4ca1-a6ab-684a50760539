import { flatten, get, isObject, isString, uniq } from 'lodash';

import { preDelDetailData } from '@/components/relational-path/use-data-source-hook';
import { getRoles, isEdge, isPerson } from '@/shared/components/_helpers/data-parser/data-node';
import { getTenderRelationLevelStyle } from '@/shared/config/bidding-investigation-detail.config';
import { Tooltip } from 'ant-design-vue';
import RelationalPath from '@/components/relational-path';
import QTag from '@/components/global/q-tag';
import QIcon from '@/components/global/q-icon';

import openSameInfoDrawer from '../../relational-table-view/same-info-drawer';
import styles from '../relations-dimension.module.less';

export const VIEW_MODE_OPTIONS = [
  {
    value: 'chart',
    label: '图谱',
  },
  {
    value: 'list',
    label: '列表',
  },
];

export const TABS_CONFIG_MAP = {
  BiddingCompanyRelationship: {
    title: '投资任职关联',
    tipsPrefix: `招标排查企业之间包含相同的`,
  },
  BiddingCompanyRelationship2: {
    title: '潜在关联/利益关联方',
    tipsPrefix: `招标排查企业之间包含`,
  },
};

export const getDirection = (relation) => {
  if (relation.direction === 1) {
    return 'right';
  }
  if (relation.direction === -1) {
    return 'left';
  }
  return 'null';
};

/**
 * @param relations 关联
 * @param item 源数据完整对象
 * @returns
 */
const parseRelationsToPath = (relations: any[], itemData: Record<string, any>) => {
  return relations.map((relation) => {
    // 判断是edge
    if (isEdge(relation)) {
      return {
        ...relation,
        type: 'edge',
        direction: getDirection(relation.direction),
        render: () => {
          if (!relation.roles && !relation?.data) {
            return relation.role;
          }
          // 历史联系方式是后端自己拼接的，这边要额外处理
          if (!relation.roles && relation.data && relation.role) {
            relation.roles = relation.data;
          }
          return relation.roles?.map((role, roleIndex) => {
            const renderLabel = (roleData, idx, showMore) => {
              const labelBlock = [<span style={{ cursor: showMore ? 'pointer' : 'default' }}>{roleData.role}</span>];
              if (relation.roles.length >= 2 && idx < relation.roles.length - 1) {
                labelBlock.push(<span>、</span>);
              }
              return labelBlock;
            };
            if (isObject(role) as any) {
              const data = role.type === 'Mail' ? role : role.data;
              const { isShowMore, handleShowMore } = preDelDetailData(
                data,
                relations.map((item) => {
                  return {
                    ...item,
                    id: item['Company.keyno'] || item['Person.keyno'],
                    label: item['Company.name'] || item['Person.name'],
                  };
                })
              );
              let tipData = role.data;
              if (role.type === 'ContactNumber') {
                tipData = getRoles(role.data);
              } else if (role.type === 'Address') {
                tipData = role.data.map(({ address }) => address);
              }
              const tipInfo = `${tipData.join('、')}`;
              const showMore =
                ['ContactNumber', 'Address', 'HisLegal', 'HisEmploy', 'HisInvest', 'Mail'].includes(role.type) && isShowMore(role.type);

              const HisEdge = ['HisLegal', 'HisEmploy', 'HisInvest', 'Mail'].includes(role.type);
              return (
                <Tooltip>
                  <span slot="title">
                    {tipInfo}{' '}
                    {showMore ? (
                      <a onClick={() => handleShowMore(role.type, HisEdge ? relation : null)} style={{ whiteSpace: 'nowrap' }}>
                        查看更多
                      </a>
                    ) : null}
                  </span>
                  {renderLabel(role, roleIndex, showMore)}
                </Tooltip>
              );
            }
            return <Tooltip title={role}>{role}</Tooltip>;
          });
        },
      };
    }
    if (isPerson(relation)) {
      return {
        ...relation,
        type: 'node',
        label: relation['Person.name'],
        name: relation['Person.name'],
        id: relation['Person.keyno'] || new Date().getTime(),
      };
    }
    return {
      label: relation['Company.name'],
      name: relation['Company.name'],
      id: relation['Company.keyno'],
      type: 'node',
    };
  });
};

export const getColumns = (isDirectRelation = true, relationsPath: any[] = [], showSteps = true) => {
  // 潜在关联/利益关联方（疑似关联关系）
  if (!isDirectRelation) {
    return [
      {
        title: '企业名称',
        width: 260,
        scopedSlots: { customRender: 'CompanyName' },
      },
      {
        title: '关联企业',
        width: 260,
        scopedSlots: { customRender: 'RelationalCompanyName' },
      },
      {
        title: '关联类型',
        width: 160,
        scopedSlots: { customRender: 'RelationalTypes' },
      },
      {
        title: '关联详情',
        width: 400,
        customRender: (value, record) => {
          /**
           * 查看更多：关系详情弹窗
           * @param roleType
           */
          const openDrawer = (roleType: string) => {
            if (!roleType) {
              return;
            }
            openSameInfoDrawer({
              record,
              type: roleType,
            });
          };

          if (record.relations?.length) {
            return record.relations?.map((relations, index, source) => {
              const key = [record.startCompanyKeyno, record.endCompanyKeyno, 'path', index].join('-');
              // data 包含的额外信息（例如：返回的结果数量，用来展示更多信息、弹窗等）
              const data = record.data[index];

              // 关联路径
              if (data === null) {
                const elements = parseRelationsToPath(relations, record);
                const typeList =
                  record.type ||
                  flatten(
                    relations
                      .filter((relation) => relation.roles)
                      .map((item) => {
                        return item.roles.map((role) => role.type);
                      })
                  );
                return (
                  <div class={styles.relationWrapper}>
                    <div class={styles.title} v-show={source.length > 1}>
                      路径 {index + 1}
                    </div>
                    <div class={styles.content}>
                      <RelationalPath key={key} namespace={key} elements={elements} type={typeList} />
                    </div>
                  </div>
                );
              }

              // data 为数组：展示更多弹窗
              if (Array.isArray(data)) {
                const originalRelations =
                  relationsPath.filter(
                    (item) =>
                      item.type === 'ContactNumber' &&
                      item.endCompanyKeyno === record.endCompanyKeyno &&
                      item.startCompanyKeyno === record.startCompanyKeyno
                  )[0]?.relations?.[0] || [];
                const isMail = relations.find((v) => v.type === 'Mail');
                const { isShowMore, handleShowMore } = preDelDetailData(isMail ? { ...record, data } : data, originalRelations);

                const list = uniq(
                  data.map((item) => {
                    if (isString(item)) {
                      return item;
                    }
                    if (item.t) {
                      return item.t;
                    }
                    if (item.address) {
                      return item.address;
                    }
                    return String(item);
                  })
                );

                const edgeInfo = record.relations[index]?.find((v) => v?.role) || {};
                const countText = edgeInfo.role ? `${edgeInfo.role}: ` : null;
                const typeList = record.relations[index]?.map((item) => item.type).filter((item) => item);
                const tagData = getTenderRelationLevelStyle(typeList);
                return (
                  <div class={styles.relationWrapper} style={{ whiteSpace: 'pre-wrap' }} key={key}>
                    <div class={styles.title} v-show={source.length > 1}>
                      路径 {index + 1}:
                    </div>
                    <div class={styles.content}>
                      <QTag v-show={!!tagData.label} style={{ ...tagData.style, marginLeft: '5px', transform: 'translateY(-1px)' }}>
                        {tagData.label}
                      </QTag>
                      <span>{countText}</span>
                      <span>{list.join('、')}</span>
                      {['ContactNumber', 'Address', 'Mail'].includes(edgeInfo.type) && isShowMore(edgeInfo.type) ? (
                        <a onClick={() => handleShowMore(edgeInfo.type)} style={{ marginLeft: '5px' }}>
                          查看更多
                        </a>
                      ) : null}
                    </div>
                  </div>
                );
              }

              // data 为对象：展示数量
              if (isObject(data)) {
                // data 为对象: 相同专利信息、相同软件著作权等
                const total = get(data, 'Paging.TotalRecords');
                const edgeInfo = relations.find((v) => v?.role) || {};
                const countText = `${edgeInfo.role || ''}数量: ${total}`;
                const typeList = relations.map((item) => item.type).filter((item) => item);
                const tagData = getTenderRelationLevelStyle(typeList);

                return (
                  <div class={[styles.relationWrapper, 'flex', 'items-center']} style={{ gap: '10px' }} key={key}>
                    <div class={styles.title} v-show={source.length > 1}>
                      路径 {index + 1}
                    </div>
                    <div class={styles.content}>
                      {tagData.label ? <QTag style={{ ...tagData.style, marginLeft: '5px' }}>{tagData.label}</QTag> : null}
                      {countText}
                      {total ? (
                        <a style={{ marginLeft: '5px' }} class="whitespace-nowrap" onClick={() => openDrawer(edgeInfo.type)}>
                          查看更多 <QIcon type="icon-a-xianduanyou" />
                        </a>
                      ) : null}
                    </div>
                  </div>
                );
              }

              return '';
            });
          }
          return '-';
        },
      },
    ];
  }

  // 投资任职关联（直接关联关系）
  const directRelationColumns = [
    {
      title: '企业名称',
      width: 260,
      scopedSlots: { customRender: 'CompanyName' },
    },
    {
      title: '关联类型',
      width: 160,
      scopedSlots: { customRender: 'DirectRelationalTypes' }, // FIXME: 解析角色数据不正确（投资100% === 持股/投资关联）
    },
    {
      title: '关联层级',
      width: 100,
      dataIndex: 'steps',
    },

    {
      title: '关联企业',
      width: 260,
      scopedSlots: { customRender: 'RelationalCompanyName' },
    },

    {
      title: '关联链',
      scopedSlots: { customRender: 'RelationalPath' },
    },
    {
      title: '图谱',
      width: 70,
      scopedSlots: { customRender: 'RelatonalChart' },
    },
  ];

  if (showSteps) {
    return directRelationColumns;
  }

  return directRelationColumns.filter((v) => v.title !== '关联层级');
};
