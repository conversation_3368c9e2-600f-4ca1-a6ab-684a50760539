import { computed, defineComponent, onMounted, PropType, ref } from 'vue';
import { keyBy, cloneDeep } from 'lodash';
import { Tabs, Tooltip } from 'ant-design-vue';

import QCard from '@/components/global/q-card';
import SwitchButton from '@/shared/components/switch-button';
import QIcon from '@/components/global/q-icon';
import { createTrackEvent } from '@/config/tracking-events';
import { getRelationRelated } from '@/shared/components/_helpers/data-parser';
import { useBiddingSetting } from '@/shared/composables/use-bidding-detail';

import ChartView from '../relational-chart-view';
import TableView from '../relational-table-view';
import { getColumns, TABS_CONFIG_MAP, VIEW_MODE_OPTIONS } from './config';
import styles from './relations-dimension.module.less';

const RelationsDimension = defineComponent({
  name: 'RelationsDimension',
  props: {
    /**
     * subDimension
     */
    data: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    /**
     * result
     */
    dataSource: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const { getSetting, setting } = useBiddingSetting();
    const getTips = (key) => {
      const TypesMap = keyBy(keyBy(setting.value, 'key')?.BiddingCompanyRelation?.subDimensionList, 'key');
      return TypesMap?.[key]?.types
        ?.map((item) => item.name)
        .filter((n) => !!n)
        .join('、');
    };

    // 深度关系排查，选中的tab
    const activeBiddingRelationIndex = ref<number>(0);
    // 详情处理后的数据
    const biddingRelationTabs = ref<Record<string, any>>([]);

    const getBiddingRelationData = () => {
      let activeIndex = -1;
      let sunCount = 0;
      biddingRelationTabs.value = props.data?.map((tab: any, index: number) => {
        const isDirectRelation = tab.key === 'BiddingCompanyRelationship';

        const tabData = tab.data ? cloneDeep(tab.data) : []; // NOTE: parseRelationsData 会修改原始数据

        const { relationsPath, relationsCount, relationList } = getRelationRelated(isDirectRelation, tabData);
        const relationListCount = relationList.length || 0;
        if (activeIndex === -1 && relationListCount) {
          activeIndex = index;
        }
        sunCount += relationListCount;
        return {
          ...tab,
          isDirectRelation,
          relationsPath,
          relationsCount,
          relationList,
          relationListCount,
        };
      });
      // 直接展示数据量大于0的第一个tab
      activeBiddingRelationIndex.value = activeIndex;

      // 重新生成数据后取数据的length，count可能对不上
      emit('resetCount', sunCount);
    };

    const activeBiddingRelation = computed(() => {
      return biddingRelationTabs.value[activeBiddingRelationIndex.value];
    });

    const activeData = computed(() => biddingRelationTabs.value[activeBiddingRelationIndex.value]);

    /**
     * 视图模式
     */
    const viewMode = ref<'chart' | 'list'>('chart');

    /**
     * 无数据
     */
    const hasNoData = computed(() => {
      return activeData.value?.relationsCount === 0;
    });

    onMounted(async () => {
      // 获取设置内容的信息，用于展示tooltip
      await getSetting();
      // 处理关系途径
      getBiddingRelationData();
    });

    return {
      viewMode,
      activeBiddingRelationIndex,
      activeData,
      biddingRelationTabs,
      getTips,
      hasNoData,
      activeBiddingRelation,
    };
  },
  render() {
    if (!this.activeData) {
      return null;
    }
    const tabContentRenderer = () => {
      if (this.hasNoData) {
        return this.$slots.empty;
      }

      const columnsConfig = getColumns(this.activeData?.isDirectRelation, this.activeData?.relationsPath);

      return (
        <keep-alive>
          {this.viewMode === 'chart' ? (
            <ChartView key="chart" relationsPath={this.activeData.relationsPath} activeBiddingRelation={this.activeBiddingRelation} />
          ) : null}
          {this.viewMode === 'list' ? <TableView key="list" dataSource={this.activeData.relationList} columns={columnsConfig} /> : null}
        </keep-alive>
      );
    };

    return (
      <QCard titleBorder={false} headerStyle={{ padding: 0, marginTop: '-9px' }} bodyStyle={{ padding: 0 }}>
        <div slot="title">
          <Tabs
            class={styles.tabs}
            activeKey={this.activeBiddingRelationIndex}
            onTabClick={(currentIndex: number) => {
              this.activeBiddingRelationIndex = currentIndex;
              const currentTabKey = this.biddingRelationTabs[currentIndex].key;
              this.$track(createTrackEvent(6939, '招标排查详情', TABS_CONFIG_MAP[currentTabKey]?.title, '深度关系排查'));
            }}
          >
            {this.biddingRelationTabs.map((tab: any, index) => {
              const tips = TABS_CONFIG_MAP[tab.key]?.tipsPrefix + this.getTips(tab.key);

              return (
                <Tabs.TabPane key={index}>
                  <div slot="tab" style={{ fontSize: '14px' }}>
                    <span>{TABS_CONFIG_MAP[tab.key]?.title}</span>
                    <span style={{ color: '#127bed' }}> {tab?.relationListCount || 0} </span>

                    <Tooltip placement="bottomLeft">
                      <div slot="title" domPropsInnerHTML={tips}></div>
                      <QIcon style={{ color: '#d8d8d8', marginRight: '0' }} type="icon-zhushi" />
                    </Tooltip>
                  </div>
                </Tabs.TabPane>
              );
            })}
          </Tabs>
        </div>
        <div slot="extra">
          <SwitchButton
            v-show={!this.hasNoData}
            value={this.viewMode}
            options={VIEW_MODE_OPTIONS}
            onChange={(currentViewMode: 'chart' | 'list', currentViewIndex: number) => {
              this.viewMode = currentViewMode;
              const trackLabel = VIEW_MODE_OPTIONS[currentViewIndex].label;
              this.$track(createTrackEvent(6939, '招标排查详情', trackLabel, '深度关系排查'));
            }}
          />
        </div>

        {tabContentRenderer()}
      </QCard>
    );
  },
});

export default RelationsDimension;
