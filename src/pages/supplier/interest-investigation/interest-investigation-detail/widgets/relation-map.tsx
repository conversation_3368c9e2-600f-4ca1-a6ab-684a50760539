import { defineComponent, ref } from 'vue';
import { cloneDeep } from 'lodash';

import QCard from '@/components/global/q-card';
import SwitchButton from '@/shared/components/switch-button';
import ChartView from '@/pages/supplier/bidding-investigation/widgets/relational-chart-view';
import TableView from '@/pages/supplier/bidding-investigation/widgets/relational-table-view';
import { getRelationRelated } from '@/shared/components/_helpers/data-parser';
import { getColumns } from '@/pages/supplier/bidding-investigation/widgets/relations-dimension/config';

const VIEW_MODE_OPTIONS = [
  {
    value: 'chart',
    label: '图谱',
  },
  {
    value: 'list',
    label: '列表',
  },
];

const RelationMap = defineComponent({
  name: 'RelationMap',
  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    dimensionType: {
      type: String,
      required: true,
    },
  },
  setup() {
    const viewMode = ref('chart');
    const changeViewMode = (mode: 'chart' | 'list', index: number) => {
      viewMode.value = mode;
    };
    return {
      viewMode,
      changeViewMode,
    };
  },
  render() {
    const isDirectRelation = !['ContactWay', 'GuaranteeRelation', 'OtherRelations'].includes(this.dimensionType);
    const showSteps = ['CrossShareHolding'].includes(this.dimensionType);
    const pathList = cloneDeep(this.dataSource);
    const { relationsPath, relationList } = getRelationRelated(isDirectRelation, pathList);
    const columnsConfig = getColumns(isDirectRelation, relationsPath, showSteps);

    return (
      <QCard title=" " titleBorder={false} headerStyle={{ padding: '0 15px 0' }} bodyStyle={{ padding: '0 15px 15px' }}>
        <div slot="extra">
          <SwitchButton value={this.viewMode} options={VIEW_MODE_OPTIONS} onChange={this.changeViewMode} />
        </div>
        <keep-alive>
          {this.viewMode === 'chart' ? <ChartView relationsPath={relationsPath} /> : null}
          {this.viewMode === 'list' ? <TableView key="list" dataSource={relationList} columns={columnsConfig} /> : null}
        </keep-alive>
      </QCard>
    );
  },
});

export default RelationMap;
